# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Start development server**: `npm run dev` or `npm start` (runs on localhost:8080)
- **Build for production**: `npm run build` (outputs to `dist/` with `/shopp/` public path)
- **Install dependencies**: `npm install`

## Build Configuration Notes

- Development server runs on port 8080 (configurable via environment)
- Production builds are configured for deployment under `/shopp/` path
- Source maps disabled in production for security
- Bundle analyzer available with `npm run build --report`

## Project Architecture

This is a Vue.js 2.x mobile web application built with Webpack 3. The project follows a modular structure with comprehensive internationalization support.

### Core Technologies
- Vue.js 2.6.12 with Vue Router and Vuex
- Vant UI component library for mobile interfaces
- Vue-i18n for internationalization (supports 11 languages)
- Axios for HTTP requests
- jQuery for legacy compatibility
- Webpack 3 for bundling

### Directory Structure
- `src/views/` - Main application views organized by feature
  - `task/` - Task management (index, list, show)
  - `user/` - User center with nested routes (wallet, settings, reports, etc.)
- `src/common/` - Shared utilities and managers
  - `Model.js` - API layer and business logic
  - `UserManager.js` - User authentication and management
  - `CurrencyManager.js` - Currency formatting and display
  - `Axios.js` - HTTP client configuration
- `src/components/` - Reusable Vue components
- `src/i18n/` - Internationalization files for 11 languages
- `src/store/` - Vuex state management with localStorage persistence
- `src/mixins/` - Vue mixins (CurrencyMixin for currency formatting)

### Routing Architecture
- Nested routing structure with authentication guards
- Route-based authentication using `requiresAuth` meta fields
- User center uses nested child routes under `/user`
- Dynamic route parameters for task and user management

### State Management
- Vuex store with localStorage persistence for most data
- User authentication state managed separately (not persisted)
- Global state includes InitData, UserInfo, BankCardList, and various UI states

### Internationalization
The app supports 11 languages with both application and UI component translations:
- Chinese (Simplified/Traditional)
- English, Spanish, Japanese
- Vietnamese, Thai, Indonesian
- Malay, Portuguese, Hindi (yd)

Language switching updates both Vue-i18n locale and Vant component locale.

### Authentication System
- Token-based authentication stored in localStorage
- Route guards prevent access to protected routes
- Automatic user info fetching on app initialization
- Session management with token validation

### Build Configuration
- Development server runs on port 8080 (configurable via environment)
- Production builds are configured for deployment under `/shopp/` path
- Source maps disabled in production for security
- Bundle analyzer available with `npm run build --report`
- Static assets organized under `static/` directory
- Webpack dev server with hot reload support

### API Integration
- Centralized API calls through `Model.js`
- Axios interceptors for authentication and error handling
- Support for file uploads and image processing
- Currency and user data management utilities

## Key Patterns
- All routes with user data require authentication
- State persistence uses localStorage for most data except sensitive user info
- Currency formatting handled through global mixin
- Multi-language asset support (images, icons per language)
- Mobile-first responsive design using Vant components

## Important Development Notes

### Router Configuration
- Uses Vue Router with route guards for authentication
- Routes with `requiresAuth: true` require valid token in localStorage
- Nested routing structure under `/user` for user center functionality
- Route guards automatically fetch user info when token exists but user data missing

### Global Prototypes and Utilities
- `$Model` - Centralized API layer through Model.js
- `$UserManager` - User authentication and session management  
- `$Currency` - Currency formatting via CurrencyManager.js
- `$getIcon` - Returns static asset paths for icons
- Global CurrencyMixin available in all components

### Language Support
- 11 languages supported with Vue-i18n integration
- Vant component locale automatically synced with app locale
- Language-specific assets (logos, images) organized by locale code
- `$SetLanguage` method available globally for language switching