import store from '@/store'
import Model from '@/common/Model'

/**
 * 用户信息管理工具类
 * 提供全局用户信息的获取、更新、清除等功能
 */
class UserManager {
  
  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息对象或null
   */
  getCurrentUser() {
    return store.state.UserInfo || null;
  }
  
  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!(localStorage['Token'] && this.getCurrentUser());
  }
  
  /**
   * 获取用户Token
   * @returns {string|null} Token或null
   */
  getToken() {
    return localStorage['Token'] || null;
  }
  
  /**
   * 获取用户ID
   * @returns {string|null} 用户ID或null
   */
  getUserId() {
    return localStorage['UserId'] || null;
  }
  
  /**
   * 刷新用户信息
   * @param {Function} callback 回调函数
   * @returns {Promise} Promise对象
   */
  refreshUserInfo(callback) {
    return new Promise((resolve, reject) => {
      if (!this.getToken()) {
        const error = { code: 0, code_dec: '用户未登录' };
        callback && callback(error);
        reject(error);
        return;
      }
      
      Model.GetUserInfo((data) => {
        callback && callback(data);
        if (data.code === 1) {
          resolve(data);
        } else {
          reject(data);
        }
      });
    });
  }
  
  /**
   * 更新用户信息到全局状态
   * @param {Object} userInfo 用户信息对象
   */
  updateUserInfo(userInfo) {
    store.dispatch('UpdateUserInfo', userInfo);
    // 用户信息不进行持久化保存，只保存在内存中
    // localStorage['UserInfo'] = JSON.stringify(userInfo);
    console.log('用户信息已更新到全局状态（仅内存）');
  }
  
  /**
   * 清除用户信息
   */
  clearUserInfo() {
    // 清除localStorage中的用户相关数据
    localStorage.removeItem('Token');
    localStorage.removeItem('UserInfo');
    localStorage.removeItem('UserId');
    localStorage.removeItem('Uid');
    localStorage.removeItem('MiName');
    localStorage.removeItem('BankCardList');
    localStorage.removeItem('StatisticalData');
    
    // 清除Vuex store中的用户相关数据
    store.dispatch('UpdateUserInfo', '');
    store.dispatch('UpdateBankCardList', []);
    store.dispatch('UpdateStatisticalData', '');
    
    console.log('用户信息已清除（包括内存和localStorage残留）');
  }
  
  /**
   * 设置登录信息
   * @param {Object} loginData 登录返回的数据
   */
  setLoginInfo(loginData) {
    if (loginData && loginData.code === 1) {
      const userInfo = loginData.info;
      
      // 保存基本登录信息
      localStorage['Token'] = userInfo.token;
      localStorage['UserId'] = userInfo.userid;
      localStorage['Uid'] = userInfo.uid;
      localStorage['MiName'] = userInfo.username;
      
      // 更新用户信息到全局状态（仅内存）
      this.updateUserInfo(userInfo);

      console.log('登录信息已设置（用户信息仅保存在内存中）');
    }
  }
  
  /**
   * 检查并自动刷新用户信息
   * 如果有Token但没有用户信息，自动获取
   * @returns {Promise} Promise对象
   */
  async autoRefreshUserInfo() {
    const token = this.getToken();
    const userInfo = this.getCurrentUser();
    
    if (token && !userInfo) {
      console.log('检测到Token但无用户信息，正在自动获取...');
      try {
        await this.refreshUserInfo();
        console.log('用户信息自动获取成功');
        return true;
      } catch (error) {
        console.error('用户信息自动获取失败:', error);
        // 如果获取失败，可能是Token失效，清除相关数据
        this.clearUserInfo();
        return false;
      }
    }
    
    return !!userInfo;
  }
  
  /**
   * 获取用户特定字段的值
   * @param {string} field 字段名
   * @param {*} defaultValue 默认值
   * @returns {*} 字段值或默认值
   */
  getUserField(field, defaultValue = null) {
    const userInfo = this.getCurrentUser();
    return userInfo && userInfo[field] !== undefined ? userInfo[field] : defaultValue;
  }
  
  /**
   * 获取用户头像
   * @returns {string} 头像路径
   */
  getUserAvatar() {
    return this.getUserField('header', 'head_1.png');
  }
  
  /**
   * 获取用户名
   * @returns {string} 用户名
   */
  getUsername() {
    return this.getUserField('username', '');
  }
  
  /**
   * 获取用户邀请码
   * @returns {string} 邀请码
   */
  getInviteCode() {
    return this.getUserField('idcode', '');
  }
  
  /**
   * 获取用户余额
   * @returns {number} 余额
   */
  getUserBalance() {
    return parseFloat(this.getUserField('balance', 0));
  }
  
  /**
   * 监听用户信息变化
   * @param {Function} callback 回调函数
   */
  watchUserInfo(callback) {
    return store.watch(
      (state) => state.UserInfo,
      (newUserInfo, oldUserInfo) => {
        callback(newUserInfo, oldUserInfo);
      },
      { deep: true }
    );
  }
}

// 创建单例实例
const userManager = new UserManager();

export default userManager;
