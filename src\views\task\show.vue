<template>
  <div class="page-container">
    <div class="task-detail">
      <!-- 头部导航 -->
      <van-nav-bar
        fixed
        :border="false"
        left-arrow
        :title="$t('task.show[18]')"
        @click-left="$router.go(-1)"
        class="detail-nav"
      >
        <template #right>
          <van-icon name="share-o" class="share-icon" />
        </template>
      </van-nav-bar>

      <!-- 商品图片 -->
      <div class="product-image-section" v-if="infoData && infoData.main_image">
        <img
          :src="getProductImage()"
          class="product-image"
          :alt="$t('task.show[19]')"
        />
      </div>

      <!-- 商品信息 -->
      <div class="product-info-section" v-if="infoData">
        <div class="cashback-info">
          <span class="cashback-title">{{ $t('task.show[21]') }}</span>
          <span class="cashback-amount">{{ $Currency.formatAmount(infoData.task_commission) }}</span>
        </div>

        <div class="product-details">
          <div class="product-name-container">
            <div class="product-badge"></div>
            <div class="product-name-text">
              <p class="product-name">{{ infoData.title || infoData.content || $t('task.show[20]') }}</p>
            </div>
          </div>
          <div class="product-meta">
            <span class="product-price">{{ $t('task.show[22]') }} {{ $Currency.formatAmount(infoData.purchase_price) }}</span>
            <!-- <span class ="product-sold">{{ infoData.y_surplus_number || infoData.surplus_number || 0 }}K {{ $t('task.show[23]') }}</span> -->
          </div>

          <!-- 任务等级要求 -->
          <div class="task-level-info" v-if="infoData.task_level">
            <div class="level-requirement">
              <span class="level-label">{{ $t('task.show[36]') }}:</span>
              <span class="level-value" :class="getLevelStatusClass()">
                {{ getTaskLevelName() }}
              </span>
              <span class="level-status" v-if="UserInfo && UserInfo.vip_level < infoData.task_level">
                ({{ $t('task.show[37]') }})
              </span>
            </div>
          </div>
        </div>

        <!-- 四个功能按钮 -->
        <div class="action-steps">
          <div class="step-item">
            <div class="step-icon member-icon">
              <img src="@/static/images/Group 9226.png" :alt="$t('home.processSteps[0]')" />
            </div>
            <span class="step-text">{{ $t('home.processSteps[0]') }}</span>
          </div>
          <div class="step-arrow">
            <img src="@/static/images/arrow.png" alt="→" />
          </div>
          <div class="step-item">
            <div class="step-icon product-icon">
              <img src="@/static/images/Group 9227.png" :alt="$t('home.processSteps[1]')" />
            </div>
            <span class="step-text">{{ $t('home.processSteps[1]') }}</span>
          </div>
          <div class="step-arrow">
            <img src="@/static/images/arrow.png" alt="→" />
          </div>
          <div class="step-item">
            <div class="step-icon order-icon">
              <img src="@/static/images/Frame.png" :alt="$t('home.processSteps[2]')" />
            </div>
            <span class="step-text">{{ $t('home.processSteps[2]') }}</span>
          </div>
          <div class="step-arrow">
            <img src="@/static/images/arrow.png" alt="→" />
          </div>
          <div class="step-item">
            <div class="step-icon commission-icon">
              <img src="@/static/images/Group 92281.png" :alt="$t('home.processSteps[3]')" />
            </div>
            <span class="step-text">{{ $t('home.processSteps[3]') }}</span>
          </div>
        </div>
      </div>

      <!-- 商品详情图片区域 -->
      <div class="detail-image-section" v-if="infoData && infoData.detail_image">
        <div
          v-for="(imageUrl, index) in getDetailImages()"
          :key="index"
          class="detail-image-item"
        >
          <img
            :src="imageUrl"
            class="detail-image"
            :alt="`${$t('task.show[19]')}${index + 1}`"
            @error="handleDetailImageError"
          />
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="bottom-actions">
        <!-- 已登录用户 -->
        <div v-if="isLogin" class="button-container">
          <!-- 可以购买的情况 -->
          <van-button
            class="buy-button"
            type="primary"
            :loading="isSubmit"
            :loading-text="$t('task.show[14]')"
            @click="onSubmit"
            v-if="canPurchase"
          >
            {{ $t('task.show[24]') }} {{ $Currency.formatAmount(infoData && infoData.purchase_price) }}
          </van-button>

          <!-- 不能购买的情况 -->
          <van-button
            class="buy-button disabled-button clickable-hint"
            type="default"
            @click="onDisabledButtonClick"
            v-else
          >
            <span class="button-text">{{ getDisabledButtonText() }}</span>
            <span class="click-hint">{{ $t('task.show[45]') }}</span>
          </van-button>

          <!-- 状态提示 -->
          <div class="status-tip" v-if="!canPurchase">
            {{ getStatusTipText() }}
          </div>
        </div>

        <!-- 未登录用户 -->
        <van-button
          class="buy-button"
          type="primary"
          to="/login"
          v-else
        >
          {{ $t("task.show[16]") }}
        </van-button>
      </div>

      <van-loading class="DataLoad" size="60px" vertical v-if="isLoad">{{
        $t("task.show[13]")
      }}</van-loading>

      <!-- 购买确认对话框 -->
      <van-dialog
        v-model="showConfirmDialog"
        :title="$t('task.show[25]')"
        show-cancel-button
        :confirm-button-text="$t('task.show[26]')"
        :cancel-button-text="$t('task.show[27]')"
        @confirm="confirmPurchase"
        @cancel="cancelPurchase"
      >
        <div style="padding: 20px; text-align: center;">
          <p>{{ $t('task.show[28]') }}</p>
          <div style="background: #f8f9fa; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
              <span style="color: #666; font-size: 14px;">{{ $t('task.show[34]') }}</span>
              <span style="color: #ff0000; font-weight: bold; font-size: 18px;">
                {{ $Currency.formatAmount(infoData && infoData.purchase_price) }}
              </span>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="color: #666; font-size: 14px;">{{ $t('task.show[35]') }}</span>
              <span style="color: #ff6b35; font-weight: bold; font-size: 16px;">
                +{{ $Currency.formatAmount(infoData && infoData.task_commission) }}
              </span>
            </div>
          </div>
          <p style="color: #666; font-size: 14px;">{{ $t('task.show[29]') }}</p>
        </div>
      </van-dialog>

      <!-- 购买成功对话框 -->
      <van-dialog
        v-model="showSuccessDialog"
        :title="$t('task.show[30]')"
        :confirm-button-text="$t('task.show[31]')"
        @confirm="onPurchaseSuccess"
      >
        <div style="padding: 20px; text-align: center;">
          <van-icon name="success" size="50" color="#07c160" style="margin-bottom: 15px;" />
          <p>{{ $t('task.show[32]') }}</p>
          <p style="color: #666; font-size: 14px;">{{ $t('task.show[33]') }}</p>
        </div>
      </van-dialog>
    </div>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import Html2Canvas from "html2canvas";
export default {
  name: "Show",
  components: {},
  props: ["taskId"],
  data() {
    return {
      isLoad: true,
      infoData: "",
      conditionArr: [],
      isLogin: localStorage["Token"] ? true : false,
      isSubmit: false,
      docTitle: document.title,
      promoteUrl: "",
      showConfirmDialog: false,
      showSuccessDialog: false,
    };
  },
  computed: {
    // 检查是否可以购买任务
    canPurchase() {
      if (!this.infoData) return false;

      // 检查任务是否已被领取
      if (this.infoData.is_l !== 0) return false;

      // 检查任务是否还有剩余数量
      if (this.infoData.surplus_number <= 0) return false;

      // 检查会员是否过期（stime或etime任意一个为空时不支持购买任务）
      if (!this.isVipTimeValid()) {
        return false;
      }

      // 检查用户VIP等级是否满足要求（必须完全相等）
      if (this.infoData.task_level && this.UserInfo) {
        const userVipLevel = this.UserInfo.vip_level || 0;
        const requiredLevel = this.infoData.task_level;

        // 只有当用户等级完全等于任务要求等级时才能购买
        if (userVipLevel !== requiredLevel) {
          return false;
        }
      }

      return true;
    }
  },
  watch: {},
  created() {
    this.getTaskinfo();
    this.promoteUrl = `${this.InitData.setting.reg_url}/#/register/${this.UserInfo.idcode}`;
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    // 检查VIP时间字段是否有效（stime和etime都不为空）
    isVipTimeValid() {
      if (!this.UserInfo) {
        return false;
      }

      // 检查stime和etime是否都存在且不为空
      const hasValidStime = this.UserInfo.stime && this.UserInfo.stime.trim() !== '';
      const hasValidEtime = this.UserInfo.etime && this.UserInfo.etime.trim() !== '';

      console.log('任务购买VIP时间有效性检查:', {
        stime: this.UserInfo.stime,
        etime: this.UserInfo.etime,
        hasValidStime: hasValidStime,
        hasValidEtime: hasValidEtime,
        isValid: hasValidStime && hasValidEtime
      });

      return hasValidStime && hasValidEtime;
    },

    getTaskinfo() {
      this.$Model.GetTaskinfo(this.taskId, (data) => {
        this.isLoad = false;
        if (data.code == 1) {
          this.infoData = data.info;
          this.conditionArr = data.info.finish_condition || [];
        }
        this.$nextTick(() => {
          if (data.info.is_fx == 1) {
            const qrCodeElement = document.getElementById("QRCode");
            if (qrCodeElement) {
              new QRCode(qrCodeElement, {
                text: this.promoteUrl,
                width: 110,
                height: 110,
                correctLevel: QRCode.CorrectLevel.H,
              });
            }
          }
        });
      });
    },

    getProductImage() {
      if (!this.infoData || !this.infoData.main_image) {
        return '';
      }

      // 如果已经是完整URL，直接返回
      if (this.infoData.main_image.startsWith('http')) {
        return this.infoData.main_image;
      }

      // 构建完整的图片URL
      const baseUrl = (this.InitData && this.InitData.setting && this.InitData.setting.up_url) || '';
      return baseUrl + this.infoData.main_image;
    },

    getDetailImages() {
      // 优先使用 detail_image 字段（JSON字符串数组）
      if (this.infoData && this.infoData.detail_image) {
        try {
          // 解析JSON字符串数组
          const imageArray = JSON.parse(this.infoData.detail_image);
          if (Array.isArray(imageArray) && imageArray.length > 0) {
            return imageArray.map(url => {
              // 处理HTML实体编码
              return url.replace(/&amp;/g, '&');
            });
          }
        } catch (error) {
          console.error('解析detail_image失败:', error);
        }
      }

      // 备选方案：获取商品详情图片，可以是任务步骤图片或详情图片
      if (this.infoData && this.infoData.task_step && this.infoData.task_step.length > 0) {
        const firstStep = this.infoData.task_step[0];
        if (firstStep.img) {
          const baseUrl = (this.InitData && this.InitData.setting && this.InitData.setting.up_url) || '';
          return [baseUrl + firstStep.img];
        }
      }

      // 如果没有任何详情图片，返回空数组
      return [];
    },

    // 获取任务等级名称
    getTaskLevelName() {
      if (!this.infoData || !this.infoData.task_level) {
        return '';
      }

      const requiredLevel = this.infoData.task_level;
      if (this.InitData && this.InitData.UserGradeList) {
        const gradeInfo = this.InitData.UserGradeList.find(grade => grade.grade === requiredLevel);
        return gradeInfo ? gradeInfo.name : `VIP${requiredLevel}`;
      }

      return `VIP${requiredLevel}`;
    },

    // 获取等级状态样式类
    getLevelStatusClass() {
      if (!this.UserInfo || !this.infoData || !this.infoData.task_level) {
        return 'level-unknown';
      }

      const userVipLevel = this.UserInfo.vip_level || 0;
      const requiredLevel = this.infoData.task_level;

      if (userVipLevel === requiredLevel) {
        return 'level-sufficient';
      } else {
        return 'level-insufficient';
      }
    },

    // 获取禁用按钮的文本
    getDisabledButtonText() {
      if (!this.infoData) return this.$t('task.show[38]'); // "任务信息加载中"

      if (this.infoData.is_l !== 0) {
        return this.$t('task.show[39]'); // "任务已被领取"
      }

      if (this.infoData.surplus_number <= 0) {
        return this.$t('task.show[40]'); // "任务已满员"
      }

      // 检查会员是否过期
      if (!this.isVipTimeValid()) {
        return this.$t('task.show[49]'); // 会员过期提示
      }

      // 检查VIP等级是否匹配
      if (this.infoData.task_level && this.UserInfo) {
        const userVipLevel = this.UserInfo.vip_level || 0;
        const requiredLevel = this.infoData.task_level;

        if (userVipLevel !== requiredLevel) {
          return this.$t('task.show[46]'); // VIP等级不匹配
        }
      }

      return this.$t('task.show[41]'); // "暂时无法购买"
    },

    // 获取状态提示文本
    getStatusTipText() {
      if (!this.infoData) return '';

      if (this.infoData.is_l !== 0) {
        return this.$t('task.show[42]'); // "此任务已被领取，请选择其他任务"
      }

      if (this.infoData.surplus_number <= 0) {
        return this.$t('task.show[43]'); // "此任务名额已满，请关注其他任务"
      }

      // 检查会员是否过期
      if (!this.isVipTimeValid()) {
        return this.$t('task.show[50]'); // 会员过期详细提示
      }

      // 检查VIP等级是否匹配
      if (this.infoData.task_level && this.UserInfo) {
        const userVipLevel = this.UserInfo.vip_level || 0;
        const requiredLevel = this.infoData.task_level;

        if (userVipLevel !== requiredLevel) {
          let requiredGradeName = `VIP${requiredLevel}`;
          if (this.InitData.UserGradeList) {
            const gradeInfo = this.InitData.UserGradeList.find(grade => grade.grade === requiredLevel);
            if (gradeInfo && gradeInfo.name) {
              requiredGradeName = gradeInfo.name;
            }
          }
          return this.$t('task.show[47]', { requiredLevel: requiredGradeName });
        }
      }

      return this.$t('task.show[44]'); // "请稍后再试或联系客服"
    },

    handleDetailImageError(event) {
      // 详情图片加载失败时的处理
      if (event.target.dataset.errorHandled) {
        return;
      }
      event.target.dataset.errorHandled = 'true';
      // 隐藏加载失败的图片
      event.target.style.display = 'none';
    },
    onSubmit() {
      // 检查会员是否过期
      if (!this.isVipTimeValid()) {
        this.$Dialog.Confirm(
          this.$t('task.show[51]'),
          () => {
            // 用户确认续费，跳转到VIP页面
            this.$router.push("/vip");
          },
          this.$t('task.show[52]')
        );
        return;
      }

      // 检查用户会员等级是否满足任务要求（必须完全相等）
      if (this.infoData && this.infoData.task_level && this.UserInfo) {
        const userVipLevel = this.UserInfo.vip_level || 0;
        const requiredLevel = this.infoData.task_level;

        if (userVipLevel !== requiredLevel) {
          // 获取所需等级的名称
          let requiredGradeName = `VIP${requiredLevel}`;
          if (this.InitData.UserGradeList) {
            const gradeInfo = this.InitData.UserGradeList.find(grade => grade.grade === requiredLevel);
            if (gradeInfo && gradeInfo.name) {
              requiredGradeName = gradeInfo.name;
            }
          }

          const currentGradeName = this.UserInfo.useridentity || `VIP${userVipLevel}`;

          // 显示等级不匹配提示
          if (userVipLevel < requiredLevel) {
            // 等级低于要求，显示升级提示
            const html = this.$t("task.index[1]", {
              currVip: currentGradeName,
              vip: requiredGradeName,
            });

            this.$Dialog.Confirm(
              html,
              () => {
                // 用户确认升级，跳转到VIP页面并定位到所需等级
                this.$router.push({
                  path: "/vip",
                  query: {
                    targetLevel: requiredLevel
                  }
                });
              },
              this.$t("task.index[2]")
            );
          } else {
            // 等级高于要求，显示降级提示
            this.$Dialog.Toast(this.$t('task.show[48]', {
              requiredLevel: requiredGradeName,
              currentLevel: currentGradeName
            }));
          }
          return;
        }
      }

      // 会员等级完全匹配，显示购买确认对话框
      this.showConfirmDialog = true;
    },

    // 处理禁用按钮点击事件
    onDisabledButtonClick() {
      if (!this.infoData) return;

      // 检查会员是否过期
      if (!this.isVipTimeValid()) {
        this.$Dialog.Confirm(
          this.$t('task.show[51]'),
          () => {
            // 用户确认续费，跳转到VIP页面
            this.$router.push("/vip");
          },
          this.$t('task.show[52]')
        );
        return;
      }

      // 检查是否是VIP等级不匹配的情况
      if (this.infoData.task_level && this.UserInfo) {
        const userVipLevel = this.UserInfo.vip_level || 0;
        const requiredLevel = this.infoData.task_level;

        if (userVipLevel !== requiredLevel) {
          // 获取所需等级的名称
          let requiredGradeName = `VIP${requiredLevel}`;
          if (this.InitData.UserGradeList) {
            const gradeInfo = this.InitData.UserGradeList.find(grade => grade.grade === requiredLevel);
            if (gradeInfo && gradeInfo.name) {
              requiredGradeName = gradeInfo.name;
            }
          }

          const currentGradeName = this.UserInfo.useridentity || `VIP${userVipLevel}`;

          if (userVipLevel < requiredLevel) {
            // 等级低于要求，显示升级提示对话框
            const html = this.$t("task.index[1]", {
              currVip: currentGradeName,
              vip: requiredGradeName,
            });

            this.$Dialog.Confirm(
              html,
              () => {
                // 用户确认升级，跳转到VIP页面并定位到所需等级
                this.$router.push({
                  path: "/vip",
                  query: {
                    targetLevel: requiredLevel
                  }
                });
              },
              this.$t("task.index[2]")
            );
          } else {
            // 等级高于要求，显示等级不匹配提示
            this.$Dialog.Toast(this.$t('task.show[48]', {
              requiredLevel: requiredGradeName,
              currentLevel: currentGradeName
            }));
          }
          return;
        }
      }

      // 其他情况的提示
      if (this.infoData.is_l !== 0) {
        this.$Dialog.Toast(this.$t('task.show[42]')); // "此任务已被领取，请选择其他任务"
      } else if (this.infoData.surplus_number <= 0) {
        this.$Dialog.Toast(this.$t('task.show[43]')); // "此任务名额已满，请关注其他任务"
      } else {
        this.$Dialog.Toast(this.$t('task.show[44]')); // "请稍后再试或联系客服"
      }
    },
    confirmPurchase() {
      this.showConfirmDialog = false;
      this.isSubmit = true;
      this.$Model.ReceiveTask(this.taskId, (data) => {
        this.isSubmit = false;
        if (data.code == 1) {
          // 购买成功，显示成功对话框
          this.showSuccessDialog = true;
        }
      });
    },
    cancelPurchase() {
      this.showConfirmDialog = false;
    },
    onPurchaseSuccess() {
      this.showSuccessDialog = false;
      // 返回首页
      this.$router.push('/');
    },
    saveQRCode() {
      const promoteElement = document.getElementById("Promote");
      if (!promoteElement) {
        console.warn("Promote element not found");
        return;
      }
      Html2Canvas(promoteElement).then((canvas) => {
        if (window.plus) {
          var saveN = 0;
          var saveImg = (img) => {
            saveN += 1;
            var bm = new plus.nativeObj.Bitmap();
            bm.loadBase64Data(
              img,
              () => {
                bm.save(
                  "_doc/promote" + saveN + ".jpg",
                  { overwrite: true, format: "jpg" },
                  (e) => {
                    plus.gallery.save(
                      e.target,
                      (e) => {
                        this.$Dialog.Toast(this.$t("promote[7]"));
                      },
                      (err) => {
                        this.$Dialog.Toast(this.$t("promote[8]"));
                      }
                    );
                  },
                  (error) => {
                    this.$Dialog.Toast(this.$t("promote[8]"));
                  }
                );
                setTimeout(function() {
                  bm.recycle();
                }, 1000);
              },
              (err) => {
                this.$Dialog.Toast(this.$t("promote[8]"));
              }
            );
          };
          this.$Dialog.Alert(this.$t("promote[11]"), () => {
            saveImg(canvas.toDataURL().replace("data:image/png;base64,", ""));
          });
        } else {
          this.downCanvas(canvas.toDataURL());
        }
      });
    },
    downCanvas(url) {
      var a = document.createElement("a");
      var event = new MouseEvent("click");
      a.download = "promote";
      a.href = url;
      a.dispatchEvent(event);
    },
  },
};
</script>
<style scoped>
/* 页面外层容器 */
.page-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #ffffff;
  overflow: hidden;
}

/* 主容器 - 使用ScrollBox类来获得正确的滚动行为 */
.task-detail {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  box-sizing: border-box;
}

/* 导航栏样式 */
.detail-nav {
  background: #ffffff !important;
  border-bottom: none !important;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.detail-nav >>> .van-nav-bar__title {
  color: #000000;
  font-size: 17px;
  font-weight: 400; /* 改为正常字重，不加粗 */
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.detail-nav >>> .van-nav-bar__arrow {
  color: #000000;
  font-size: 18px;
}

.share-icon {
  color: #000000;
  font-size: 20px;
}

/* 商品图片区域 */
.product-image-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: #f8f8f8;
  margin-top: 46px; /* 为白底导航栏留出空间 */
  flex-shrink: 0;
  position: relative;
  padding: 0; /* 移除内边距 */
}

.product-image {
  width: 100%; /* 宽度占满容器 */
  height: auto; /* 高度自适应 */
  object-fit: contain; /* 保持图片比例，完整显示 */
  border-radius: 0; /* 移除圆角 */
  display: block; /* 确保图片正确显示 */
}

/* 商品信息区域 */
.product-info-section {
  display: flex;
  flex-direction: column;
  padding: 24px;
  background: white;
  flex-shrink: 0;
}

/* Cashback信息 */
.cashback-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 0;
}

.cashback-title {
  font-size: 16px;
  font-weight: 400;
  color: #000000;
  margin: 0;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 24px;
}

.cashback-amount {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 40px;
  text-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
}

/* 商品详情 */
.product-details {
  display: flex;
  flex-direction: column;
  margin-bottom: 32px;
  padding: 0;
}

.product-name-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 8px;
}

.product-badge {
  width: 16px;
  height: 16px;
  background: #ff3333;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 2px; /* 与文字对齐 */
}

.product-name-text {
  flex: 1;
}

.product-name {
  font-size: 12px;
  color: #7d7d7d;
  line-height: 20px;
  margin: 0;
  font-weight: 400;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 36px;
  font-weight: 800;
  background: linear-gradient(135deg, #ff0f23, #e60012);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 44px;
  text-shadow: 0 2px 6px rgba(255, 15, 35, 0.4);
  letter-spacing: -0.5px;
}

.product-sold {
  font-size: 12px;
  color: #7d7d7d;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 20px;
}

/* 任务等级信息样式 */
.task-level-info {
  margin-top: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1989fa;
}

.level-requirement {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.level-value {
  font-size: 14px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.level-value.level-sufficient {
  color: #07c160;
  background: #e8f5e8;
}

.level-value.level-insufficient {
  color: #ff3333;
  background: #ffe6e6;
}

.level-value.level-unknown {
  color: #666666;
  background: #f0f0f0;
}

.level-status {
  font-size: 12px;
  color: #ff3333;
  font-weight: 400;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 四个功能按钮 */
.action-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  padding: 20px 16px;
  margin: 0 24px 16px 24px;
  box-sizing: border-box;
  gap: 8px;
  border-radius: 12px;
}

/* 步骤项 */
.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 60px;
}

/* 步骤图标 */
.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-bottom: 6px;
}

.step-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.step-icon .van-icon {
  font-size: 24px;
  color: #1976d2;
}

/* 步骤箭头 */
.step-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin: 0 8px;
  flex-shrink: 0;
}

.step-arrow img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.step-text {
  font-size: 11px;
  color: #000000;
  line-height: 14px;
  text-align: center;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  white-space: nowrap;
}

/* 商品详情图片区域 */
.detail-image-section {
  display: flex;
  flex-direction: column;
  width: 100%;
  background: #f8f8f8;
  flex-shrink: 0;
  position: relative;
  padding: 0;
}

.detail-image-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 0; /* 图片之间无间距 */
}

.detail-image {
  width: 100%; /* 宽度占满容器 */
  height: auto; /* 高度自适应 */
  object-fit: contain; /* 保持图片比例，完整显示 */
  border-radius: 0; /* 移除圆角 */
  display: block; /* 确保图片正确显示 */
}

/* 底部操作栏 */
.bottom-actions {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: sticky;
  bottom: 0;
  width: 100%;
  /* min-height: 80px; */
  background: white;
  padding: 16px 24px 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
  margin-top: auto; /* 推到底部 */
}

/* 按钮容器 */
.button-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 购买按钮 */
.buy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 48px;
  background: #ff3333 !important;
  border: none !important;
  border-radius: 24px !important;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: white !important;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  padding: 0;
}

/* 禁用状态的按钮 */
.disabled-button {
  background: #ff6b6b !important;
  color: #ffffff !important;
  cursor: pointer;
  opacity: 0.8;
  position: relative;
  flex-direction: column;
  padding: 8px 16px !important;
  height: auto !important;
  min-height: 48px;
}

.disabled-button:hover {
  opacity: 1;
}

/* 可点击提示样式 */
.clickable-hint .button-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 2px;
}

.clickable-hint .click-hint {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 400;
}

/* 添加脉动动画提示 */
.clickable-hint {
  animation: pulse-hint 2s infinite;
}

@keyframes pulse-hint {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

/* 状态提示 */
.status-tip {
  font-size: 12px;
  color: #999999;
  text-align: center;
  line-height: 18px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  margin-top: 8px;
  padding: 0 8px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  max-width: 100%;
  box-sizing: border-box;
}

.DataLoad {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
}
</style>
