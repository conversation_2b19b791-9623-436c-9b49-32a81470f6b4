import Vue from 'vue'
import {
	Locale
} from 'vant'
import VueI18n from 'vue-i18n'

import Chinese from './cn'
import ChineseFt from './ft'
import English from './en'
import Vietnam from './vi'
import Thai from './th'
import Indonesia from './id'
import India from './yd'
import Spain from './es'
import Japanese from './jp'
import Malay from './ma'
import Portugal from './pt'

import zhCN from 'vant/lib/locale/lang/zh-CN'
import zhTW from 'vant/lib/locale/lang/zh-TW'
import enUS from 'vant/lib/locale/lang/en-US'
import viVN from './locale/vi-VN'
import thTH from './locale/th-TH'
import idID from './locale/id-ID'
import ydYD from './locale/yd-YD'
import esES from 'vant/lib/locale/lang/es-ES'
import jaJP from 'vant/lib/locale/lang/ja-JP'
import maMA from './locale/ma-MA'
import ptPT from './locale/pt-PT'

/*初始化*/
const lang = localStorage['Language'] || Language
let locale = 'zh-CN'
switch (lang) {
	case 'cn':
		locale = 'zh-CN'
		Locale.use('zh-CN', zhCN)
		break;
	case 'ft':
		locale = 'zh-TW'
		Locale.use('zh-TW', zhTW)
		break;
	case 'en':
		locale = 'en-US'
		Locale.use('en-US', enUS)
		break;
	case 'id':
		locale = 'id-ID'
		Locale.use('id-ID', idID)
		break;
	case 'th':
		locale = 'th-TH'
		Locale.use('th-TH', thTH)
		break;
	case 'vi':
		locale = 'vi-VN'
		Locale.use('vi-VN', viVN)
		break;
	case 'yd':
		locale = 'yd-YD'
		Locale.use('yd-YD', ydYD)
		break;
	case 'es':
		locale = 'es-ES'
		Locale.use('es-ES', esES)
		break;
	case 'ja':
		locale = 'ja-JP'
		Locale.use('ja-JP', jaJP)
		break;
  case 'ma':
  	locale = 'ma-MA'
  	Locale.use('ma-MA', maMA)
  	break;
  case 'pt':
  	locale = 'pt-PT'
  	Locale.use('pt-PT', ptPT)
  	break;  
}

Vue.use(VueI18n)
const i18n = new VueI18n({
	locale: locale,
	messages: {
		"zh-CN": Chinese,
		"zh-TW": ChineseFt,
		"en-US": English,
		"id-ID": Indonesia,
		"th-TH": Thai,
		"vi-VN": Vietnam,
		"yd-YD": India,
		"es-ES": Spain,
		"ja-JP": Japanese,
    "ma-MA": Malay,
    "pt-PT": Portugal,
	}
})

/*切換*/
export function SetLanguage(lang) {
	i18n.locale = lang
	switch (lang) {
		case 'zh-CN':
			localStorage['Language'] = 'cn'
			Locale.use('zh-CN', zhCN)
			break;
		case 'zh-TW':
			localStorage['Language'] = 'ft'
			Locale.use('zh-TW', zhTW)
			break;
		case 'en-US':
			localStorage['Language'] = 'en'
			Locale.use('en-US', enUS)
			break;
		case 'id-ID':
			localStorage['Language'] = 'id'
			Locale.use('id-ID', idID)
			break;
		case 'th-TH':
			localStorage['Language'] = 'th'
			Locale.use('th-TH', thTH)
			break;
		case 'vi-VN':
			localStorage['Language'] = 'vi'
			Locale.use('vi-VN', viVN)
			break;
		case 'yd-YD':
			localStorage['Language'] = 'yd'
			Locale.use('yd-YD', ydYD)
			break;
		case 'es-ES':
			localStorage['Language'] = 'es'
			Locale.use('es-ES', esES)
			break;
		case 'ja-JP':
			localStorage['Language'] = 'ja'
			Locale.use('ja-JP', jaJP)
			break;
    case 'ma-MA':
    	localStorage['Language'] = 'ma'
    	Locale.use('ma-MA', maMA)
    	break;
    case 'pt-PT':
    	localStorage['Language'] = 'pt'
    	Locale.use('pt-PT', ptPT)
    	break;  
	}
}
export default i18n
