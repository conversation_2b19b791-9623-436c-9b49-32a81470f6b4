<template>
  <div class="PageBox">
    <van-nav-bar
      fixed
      :border="false"
      :left-text="webTitle"
      left-arrow
      @click-left="$router.go(-1)"
    >
    </van-nav-bar>
    <div class="ScrollBox GlobalPayOrder">
      <!-- 订单状态卡片 -->
      <div class="order-status-card">
        <div class="status-icon" :class="getStatusClass(orderInfo.status)">
          <van-icon :name="getStatusIcon(orderInfo.status)" />
        </div>
        <div class="status-text">
          <div class="status-title">{{ getStatusText(orderInfo.status) }}</div>
          <div class="status-desc">{{ getStatusDescription(orderInfo.status) }}</div>
        </div>
      </div>

      <!-- 订单信息 -->
      <div class="order-info-section">
        <div class="section-title">{{ $t('globalPay.orderNumber') }}</div>
        <div class="info-list">
          <div class="info-item">
            <span class="label">{{ $t('globalPay.orderNumber') }}</span>
            <span class="value">{{ orderInfo.order_number || orderNumber }}</span>
            <van-icon name="copy" @click="copyOrderNumber" />
          </div>
          
          <div class="info-item">
            <span class="label">{{ $t('globalPay.paymentAmount') }}</span>
            <span class="value amount">
              {{ orderInfo.currency }} {{ formatCurrency(orderInfo.amount) }}
            </span>
          </div>
          
          <div class="info-item">
            <span class="label">{{ $t('globalPay.selectPaymentMethod') }}</span>
            <span class="value">{{ orderInfo.payment_method_name }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">{{ $t('globalPay.selectCountry') }}</span>
            <span class="value">
              {{ orderInfo.country_flag }} {{ orderInfo.country_name }}
            </span>
          </div>
          
          <div class="info-item">
            <span class="label">{{ $t('common[7]') }}</span>
            <span class="value">{{ formatDateTime(orderInfo.created_at) }}</span>
          </div>
          
          <div class="info-item" v-if="orderInfo.completed_at">
            <span class="label">{{ $t('task.info[8]') }}</span>
            <span class="value">{{ formatDateTime(orderInfo.completed_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 支付信息 -->
      <div class="payment-info-section" v-if="orderInfo.payment_info">
        <div class="section-title">支付信息</div>
        <div class="payment-details">
          <div v-if="orderInfo.payment_info.qr_code" class="qr-code-container">
            <img :src="orderInfo.payment_info.qr_code" alt="QR Code" class="qr-code" />
            <div class="qr-tips">{{ $t('recharge.tips[1]') }}</div>
          </div>
          
          <div v-if="orderInfo.payment_info.payment_url" class="payment-url">
            <van-button 
              type="primary" 
              size="large" 
              block
              @click="openPaymentUrl"
            >
              {{ $t('globalPay.buttons.payNow') }}
            </van-button>
          </div>
          
          <div v-if="orderInfo.payment_info.instructions" class="payment-instructions">
            <div class="instructions-title">支付说明</div>
            <div class="instructions-content" v-html="orderInfo.payment_info.instructions"></div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button 
          v-if="canRetry" 
          type="warning" 
          size="large" 
          @click="retryPayment"
          :loading="isRetrying"
        >
          {{ $t('globalPay.buttons.retry') }}
        </van-button>
        
        <van-button 
          v-if="canCancel" 
          type="default" 
          size="large" 
          @click="cancelOrder"
          :loading="isCancelling"
        >
          {{ $t('globalPay.buttons.cancel') }}
        </van-button>
        
        <van-button 
          type="primary" 
          size="large" 
          @click="refreshStatus"
          :loading="isRefreshing"
        >
          刷新状态
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "WatchPayOrder",
  props: ["orderNumber"],
  
  data() {
    return {
      orderInfo: {},
      isRefreshing: false,
      isRetrying: false,
      isCancelling: false,
      refreshTimer: null
    };
  },
  
  computed: {
    canRetry() {
      return ['failed', 'expired'].includes(this.orderInfo.status);
    },

    canCancel() {
      return ['pending', 'processing'].includes(this.orderInfo.status);
    },

    // 网站标题
    webTitle() {
      return (this.$store.state.InitData && this.$store.state.InitData.setting && this.$store.state.InitData.setting.web_title) || 'SmartNest';
    }
  },
  
  created() {
    this.loadOrderInfo();
    this.startAutoRefresh();
  },
  
  destroyed() {
    this.stopAutoRefresh();
  },
  
  methods: {
    // 加载订单信息
    async loadOrderInfo() {
      try {
        this.$Model.GetPaymentOrderStatus({
          order_number: this.orderNumber
        }, (data) => {
          if (data.code === 1) {
            this.orderInfo = data.data || {};
          } else {
            this.$Dialog.Toast(data.msg || this.$t('globalPay.messages.networkError'));
          }
        });
      } catch (error) {
        console.error('加载订单信息失败:', error);
        this.$Dialog.Toast(this.$t('globalPay.messages.networkError'));
      }
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        pending: 'status-pending',
        processing: 'status-processing',
        completed: 'status-completed',
        failed: 'status-failed',
        cancelled: 'status-cancelled',
        expired: 'status-expired'
      };
      return classMap[status] || 'status-default';
    },
    
    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        pending: 'clock-o',
        processing: 'loading',
        completed: 'success',
        failed: 'close',
        cancelled: 'cross',
        expired: 'warning-o'
      };
      return iconMap[status] || 'info-o';
    },
    
    // 获取状态文本
    getStatusText(status) {
      return this.$t(`globalPay.status.${status}`) || status;
    },
    
    // 获取状态描述
    getStatusDescription(status) {
      const descMap = {
        pending: this.$t('globalPay.tips.processingTip'),
        processing: this.$t('globalPay.tips.processingTip'),
        completed: this.$t('globalPay.tips.successTip'),
        failed: this.$t('globalPay.messages.paymentFailed'),
        cancelled: '订单已取消',
        expired: '订单已过期'
      };
      return descMap[status] || '';
    },
    
    // 复制订单号
    copyOrderNumber() {
      this.$Util.CopyText(this.orderInfo.order_number || this.orderNumber);
      this.$Dialog.Toast(this.$t('dialog[3]'));
    },
    
    // 格式化货币
    formatCurrency(amount) {
      return this.formatLargeNumber(amount);
    },
    
    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime) return '';
      return new Date(datetime).toLocaleString();
    },
    
    // 打开支付URL
    openPaymentUrl() {
      if (this.orderInfo.payment_info && this.orderInfo.payment_info.payment_url) {
        window.open(this.orderInfo.payment_info.payment_url, '_blank');
      }
    },
    
    // 刷新状态
    async refreshStatus() {
      this.isRefreshing = true;
      await this.loadOrderInfo();
      this.isRefreshing = false;
    },
    
    // 重试支付
    async retryPayment() {
      this.isRetrying = true;
      try {
        // 重新创建订单或重试支付
        this.$router.push('/user/globalPay');
      } catch (error) {
        console.error('重试支付失败:', error);
        this.$Dialog.Toast(this.$t('globalPay.messages.networkError'));
      }
      this.isRetrying = false;
    },
    
    // 取消订单
    async cancelOrder() {
      this.isCancelling = true;
      try {
        // 调用取消订单API
        // 这里需要根据实际API实现
        this.$Dialog.Toast('订单取消功能待实现');
      } catch (error) {
        console.error('取消订单失败:', error);
        this.$Dialog.Toast(this.$t('globalPay.messages.networkError'));
      }
      this.isCancelling = false;
    },
    
    // 开始自动刷新
    startAutoRefresh() {
      if (['pending', 'processing'].includes(this.orderInfo.status)) {
        this.refreshTimer = setInterval(() => {
          this.loadOrderInfo();
        }, 10000); // 每10秒刷新一次
      }
    },
    
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    }
  },
  
  watch: {
    'orderInfo.status'(newStatus) {
      if (['completed', 'failed', 'cancelled', 'expired'].includes(newStatus)) {
        this.stopAutoRefresh();
      } else {
        this.startAutoRefresh();
      }
    }
  }
};
</script>

<style scoped>
.GlobalPayOrder {
  padding: 15px;
  background-color: #f7f8fa;
  padding-top: 60px; /* 为固定的导航栏留出空间 */
}

.order-status-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 24px;
}

.status-pending { background-color: #fff7e6; color: #fa8c16; }
.status-processing { background-color: #e6f7ff; color: #1890ff; }
.status-completed { background-color: #f6ffed; color: #52c41a; }
.status-failed { background-color: #fff2f0; color: #ff4d4f; }
.status-cancelled { background-color: #f5f5f5; color: #8c8c8c; }
.status-expired { background-color: #fff1f0; color: #ff7875; }

.status-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-desc {
  font-size: 14px;
  color: #666;
}

.order-info-section,
.payment-info-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 14px;
  color: #666;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 15px;
}

.value.amount {
  color: #1989fa;
  font-weight: 600;
}

.qr-code-container {
  text-align: center;
  padding: 20px;
}

.qr-code {
  width: 200px;
  height: 200px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.qr-tips {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.payment-url {
  margin: 20px 0;
}

.payment-instructions {
  margin-top: 20px;
}

.instructions-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.instructions-content {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.action-buttons .van-button {
  flex: 1;
}
</style>
