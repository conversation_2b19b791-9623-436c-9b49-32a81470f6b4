/**
 * 币种混入
 * 为组件提供统一的币种处理方法
 */
export default {
  computed: {
    /**
     * 获取当前币种符号
     * @returns {string} 币种符号
     */
    currentCurrency() {
      return this.$Currency.getSymbol();
    }
  },

  methods: {
    /**
     * 格式化金额显示（包含币种）
     * @param {number|string} amount 金额
     * @param {number} decimals 小数位数，默认2位
     * @returns {string} 格式化后的金额字符串
     */
    formatCurrency(amount, decimals = 2) {
      return this.$Currency.formatAmount(amount, decimals);
    },

    /**
     * 格式化金额显示（不包含币种）
     * @param {number|string} amount 金额
     * @param {number} decimals 小数位数，默认2位
     * @returns {string} 格式化后的金额字符串
     */
    formatAmount(amount, decimals = 2) {
      return this.$Currency.formatAmountOnly(amount, decimals);
    },



    /**
     * 全球化数值简写格式化
     * @param {number|string} value 数值
     * @param {Object} options 格式化选项
     * @returns {string} 格式化后的数值字符串
     */
    formatCompactNumber(value, options = {}) {
      return this.$Currency.formatCompactNumber(value, options);
    },

    /**
     * 格式化货币显示（支持简写）
     * @param {number|string} amount 金额
     * @param {Object} options 格式化选项
     * @param {boolean} options.compact 是否使用简写格式，默认false
     * @param {number} options.decimals 小数位数，默认2位
     * @param {boolean} options.showSymbol 是否显示货币符号，默认true
     * @returns {string} 格式化后的货币字符串
     */
    formatCurrencyCompact(amount, options = {}) {
      return this.$Currency.formatCurrency(amount, { compact: true, ...options });
    },

    /**
     * 格式化大数值显示（兼容旧方法）
     * @param {number|string} value 数值
     * @returns {string} 格式化后的数值字符串
     */
    formatLargeNumber(value) {
      return this.formatCompactNumber(value);
    }
  }
};
