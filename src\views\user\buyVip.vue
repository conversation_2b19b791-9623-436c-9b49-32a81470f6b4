<template>
  <div class="PageBox">
    <div class="ScrollBox">
      <van-field
        size="large"
        v-model="postData.xusername" 
        :label="$t('buyVip[1]')" 
        :placeholder="$t('buyVip[2]')"
        clearable
        class="mt15"
      />
      <van-field
        readonly
        :value="vipName"
        :label="$t('buyVip[3]')"
        :placeholder="`--${$t('buyVip[4]')}--`"
        @click="showPicker=true"
      />
      <div style="padding: 20px 16px;">
        <van-button type="danger" block style="font-size: 16px;" @click="submitBuy">{{$t('buyVip[6]')}}</van-button>
      </div>
    </div>
    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="pickerList"
        @confirm="onConfirm"
        @cancel="showPicker=false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'BuyVip',
  components: {

  },
  props: [],
  data() {
    return {
      postData: {
        xusername: '',
        grade: '',
      },
      showPicker: false,
      pickerList: '',
      vipList: '',
      vipName: '',

    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.$parent.navBarTitle = this.$t('buyVip[0]')
    this.vipList = this.InitData.UserGradeList.filter(item=>item.grade!=1)
    this.pickerList = this.vipList.flatMap(item=>item.name)
  },
  mounted() {

  },
  activated() {

  },
  destroyed() {
    
  },
  methods: {
    onConfirm(value,index) {
      this.vipName = value
      this.postData.grade = this.vipList[index].grade
      this.showPicker = false;
    },
    submitBuy() {
      if(!this.postData.xusername){
        this.$Dialog.Toast(this.$t('buyVip[2]'));
        return
      }
      if(!this.postData.grade){
        this.$Dialog.Toast(this.$t('buyVip[4]'));
        return
      }
      this.$Dialog.Confirm(this.$t('buyVip[5]',{user:this.postData.xusername,grade: this.vipName}),()=>{
        this.$Model.BuyVip(this.postData)
      })
    }
  }
}
</script>
<style scoped>
.van-cell>>>.van-cell__title{
  font-size: 14px;
}
</style>