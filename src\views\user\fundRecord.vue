<template>
  <div class="PageBox">
    <van-tabs
      :ellipsis="false"
      :border="false"
      color="#4087f1"
      background="white"
      title-active-color="#fff"
      title-inactive-color="#292929"
      line-width="0"
      v-model="tabsIndex"
      style="padding-top: 20px"
      @change="changeTabs"
    >
      <van-tab
        v-for="item in taskTabs"
        :title="item.text"
        :key="item.state"
        style="padding: 0 12px;"
      >
        <van-pull-refresh v-model="isRefresh" @refresh="onRefresh">
          <van-list
            v-model="isLoad"
            :finished="isFinished"
            :finished-text="
              listData[tabsIndex].length ? $t('vanPull[0]') : $t('vanPull[1]')
            "
            @load="onLoad"
            :class="{ Empty: !listData[tabsIndex].length }"
          >
            <div v-if="tabsIndex == 2">
              <van-cell
                class="FundItem"
                :border="false"
                v-for="(item, index) in listData[tabsIndex]"
                :key="item.dan"
                :to="`/user/recharge/${item.dan}`"
              >
                <div :class="`icon tag${tabsIndex}`" slot="icon">
                  {{ $t("fundRecord.default[3]") }}
                </div>
                <template #title>
                  <div>
                    <span>{{ item.dan }}</span>
                    <span>{{ item.adddate }}</span>
                  </div>
                  <div>
                    <span class="money">+{{ Number(item.money) }}</span>
                    <span>{{ item.status_desc }}</span>
                  </div>
                </template>
              </van-cell>
            </div>
            <div v-else>
              <van-cell
                class="FundItem"
                :border="false"
                v-for="(item, index) in listData[tabsIndex]"
                :key="item.order_id"
              >
                <div :class="`icon tag${tabsIndex}`" slot="icon">
                  {{
                    tabsIndex == 0
                      ? $t("fundRecord.default[4]")
                      : $t("fundRecord.default[5]")
                  }}
                </div>
                <template #title>
                  <div>
                    <span>{{ item.trade_number }}</span>
                    <span>{{ item.trade_time }}</span>
                  </div>
                  <div style="margin-top: 8px;">
                    <span class="money"
                      >{{ tabsIndex == 1 ? "" : item.jj || "+"
                      }}{{ item.trade_amount }}</span
                    >
                    <span>{{ item.remarks }}</span>
                  </div>
                  <!-- 显示交易描述 -->
                  <div
                    v-if="item.trade_dec"
                    style="margin-top: 5px; font-size: 12px; color: #666; word-wrap: break-word; word-break: break-all; white-space: normal; line-height: 1.4;"
                  >
                    {{ $t('fundRecord.tradeDescription') }}: {{ item.trade_dec }}
                  </div>
                </template>
              </van-cell>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  name: "FundRecord",
  components: {},
  props: ["fundState"],
  data() {
    return {
      listData: "",
      isLoad: false,
      isFinished: false,
      isRefresh: false,
      pageNo: 1,
      tabsState: 4,
      tabsIndex: 0,
      taskTabs: [
        { state: 4, text: this.$t("fundRecord.tabs[0]") },
        { state: 3, text: this.$t("fundRecord.tabs[1]") },
        { state: 1, text: this.$t("fundRecord.tabs[2]") },
      ],
    };
  },
  computed: {},
  watch: {
    $route() {
      if (this.fundState) {
        this.tabsIndex = this.taskTabs.findIndex(
          (item) => item.state == this.fundState
        );
        this.tabsState = this.fundState;
      } else {
        this.tabsState = 4;
        this.tabsIndex = 0;
      }
      this.getListData("init");
    },
  },
  created() {
    this.listData = this.taskTabs.flatMap((item) => [""]);
    if (this.fundState) {
      this.tabsIndex = this.taskTabs.findIndex(
        (item) => item.state == this.fundState
      );
      this.tabsState = this.fundState;
    }
    switch (this.fundState) {
      case "3":
        this.$parent.navBarTitle = this.$t("fundRecord.default[0]");
        break;
      case "1":
        this.$parent.navBarTitle = this.$t("fundRecord.default[1]");
        break;
      default:
        this.$parent.navBarTitle = this.$t("fundRecord.default[2]");
    }
    this.getListData("init");
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    onLoad() {
      this.getListData("load");
    },
    changeTabs(index) {
      this.tabsState = this.taskTabs[index].state;
      this.getListData("init");
      // this.$router.push(`/user/fundRecord/${this.tabsState}`)
      switch (this.tabsState) {
        case 3:
          this.$parent.navBarTitle = this.$t("fundRecord.default[0]");
          break;
        case 1:
          this.$parent.navBarTitle = this.$t("fundRecord.default[1]");
          break;
        default:
          this.$parent.navBarTitle = this.$t("fundRecord.default[2]");
      }
    },
    getListData(type) {
      this.isLoad = true;
      this.isRefresh = false;
      if (type == "load") {
        this.pageNo += 1;
      } else {
        this.pageNo = 1;
        this.isFinished = false;
      }
      if (this.tabsIndex == 2) {
        this.$Model.GetRechargeRecord(
          { state: 0, page_no: this.pageNo },
          (data) => {
            this.isLoad = false;
            if (data.code == 1) {
              if (type == "load") {
                this.listData[this.tabsIndex] = this.listData[
                  this.tabsIndex
                ].concat(data.info);
              } else {
                this.listData[this.tabsIndex] = data.info;
              }
              if (this.pageNo == data.data_total_page) {
                this.isFinished = true;
              } else {
                this.isFinished = false;
              }
            } else {
              this.listData[this.tabsIndex] = "";
              this.isFinished = true;
            }
          }
        );
      } else {
        this.$Model.FundDetails(
          { trade_type: this.tabsState, page_no: this.pageNo },
          (data) => {
            this.isLoad = false;
            if (data.code == 1) {
              if (type == "load") {
                this.listData[this.tabsIndex] = this.listData[
                  this.tabsIndex
                ].concat(data.list);
              } else {
                this.listData[this.tabsIndex] = data.list;
              }
              if (this.pageNo == data.data_total_page) {
                this.isFinished = true;
              } else {
                this.isFinished = false;
              }
            } else {
              this.listData[this.tabsIndex] = "";
              this.isFinished = true;
            }
          }
        );
      }
    },
    onRefresh() {
      this.getListData("init");
    },
  },
};
</script>
<style scoped>
.van-pull-refresh {
  width: 100%;
  height: 100%;
}
.PageBox >>> .van-tab--active {
  background-color: #ff0f23;
  color: #fff;
  border-radius: 50px;
  padding: 0 10px;
  width: 70px;
}
.PageBox >>> .van-tabs {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.PageBox >>> .van-tabs__wrap{
  height: 50px!important;
  overflow:inherit !important;
}
.PageBox >>> .van-tabs__content{
  width: 100%;
  flex: 1;
  height: 0px;
  margin-top: 10px;
}
.PageBox >>> .van-tab__pane{
  height: 100%;
  width: 100%;
}
.PageBox >>> .van-pull-refresh__track{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.PageBox >>> .van-list{
  flex: 1;
  overflow: auto;
}
.FundItem {
  background-color: #f8f8f8 !important;
  border-radius: 12px;
}
.FundItem >>> .van-cell__title div {
  color: #292929;
}
</style>
