<template>
  <div class="PageBox">
    <div class="ScrollBox">
      <div
        style="overflow: hidden;margin-top: 15px"
        v-if="userInfo.is_realname != 1"
      >
        <van-divider :hairline="false">{{
          $t("bankCard.tips[0]")
        }}</van-divider>
        <div style="padding: 15px;">
          <van-button
            block
            class="loginBtn"
            type="danger"
            style="font-size: 16px;"
            @click="$router.push('/user/set/realname')"
            >{{ $t("bankCard.default[1]") }}</van-button
          >
        </div>
      </div>
      <div v-else>
        <!-- <van-divider :hairline="false" style="text-align: center" v-if="showAdd">{{$t('bankCard.tips[1]')}}</van-divider> -->
        <div v-if="showAdd">
          <van-form class="formBox">
            <van-field
              readonly
              :value="userInfo.realname"
              :label="$t('bankCard.label[0]')"
            />
            <van-field
              readonly
              v-model.trim="postData.bank_name"
              :label="$t('bankCard.label[1]')"
              :placeholder="$t('bankCard.placeholder[0]')"
              @click="showPicker=true"
            />
            <van-field
              v-model.trim="postData.card_no"
              :label="isUSDTBank ? $t('bankCard.label[7]') : $t('bankCard.label[2]')"
              :placeholder="isUSDTBank ? $t('bankCard.placeholder[6]') : $t('bankCard.placeholder[1]')"
            />
            <!-- <van-field
            readonly
            v-model.trim="postData.bank_name"
            :label="$t('bankCard.label[1]')"
            :placeholder="$t('bankCard.placeholder[0]')"
            @click="showPicker=true"
          /> -->
          </van-form>
          <div style="padding: 15px;">
            <van-button
              block
              type="danger"
              class="loginBtn"
              @click="onSubmit"
              :loading="isSubmit"
              :loading-text="$t('bankCard.default[2]')"
              style="font-size: 16px;"
              >{{ $t("bankCard.default[3]") }}</van-button
            >
          </div>
        </div>
        <div v-else>
          <div style="padding: 10px 10px 0;">
            <van-button
              block
              type="info"
              class="loginBtn"
              style="font-size: 16px;"
              @click="showAdd = true"
              >{{ $t("usdt[2]") }}</van-button
            >
          </div>
          <div class="listBox">
            <van-cell
              class="list"
              :border="false"
              :title="`${item.bank_name} ${item.bank_branch_name}`"
              :label="
                item.card_no.replace(/^(.{4}).*(.{4})$/, '$1 **** **** $2')
              "
              icon="card"
              v-for="item in cardList"
              :key="item.id"
            />
          </div>
        </div>
      </div>
    </div>
    <van-popup class="PickerPopup" v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="bankList"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>

  </div>
</template>

<script>
export default {
  name: "BankCard",
  components: {},
  props: [],
  data() {
    return {
      postData: {
        name: "",
        bank_name: "",
        card_no: "",
        bank_id: "",
      },
      showPicker: false,
      bankList: "",
      showAdd: false,
      isSubmit: false,
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.UserInfo;
    },
    cardList() {
      return this.$store.state.BankCardList;
    },
    InitData() {
      return this.$store.state.InitData;
    },
    // 判断当前选择的银行是否为USDT
    isUSDTBank() {
      return this.postData.bank_name &&
             (this.postData.bank_name.toUpperCase().includes('USDT') ||
              this.postData.bank_name.toUpperCase().includes('TETHER'));
    },

  },
  watch: {
    // 监听InitData变化，当数据加载完成后更新银行列表
    'InitData.BanksList': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          // 直接使用银行数据，提取银行名称用于选择器
          this.bankList = newVal
            .filter(bank => bank.enabled)
            .map(bank => bank.bank);
        }
      },
      immediate: true
    }
  },
  created() {
    this.$parent.navBarTitle = this.$t("usdt[1]");
    if (this.cardList.length) {
      this.showAdd = false;
    } else {
      this.showAdd = true;
    }
    this.postData.name = this.userInfo.realname;
    // 直接使用银行数据，提取银行名称用于选择器
    if (this.InitData.BanksList && this.InitData.BanksList.length > 0) {
      this.bankList = this.InitData.BanksList
        .filter(bank => bank.enabled)
        .map(bank => bank.bank);
    }
    this.$Model.GetBankCardList();
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {




    onConfirm(value, index) {
      this.postData.bank_name = value;
      // 从启用的银行列表中找到对应的银行ID
      const enabledBanks = this.InitData.BanksList.filter(bank => bank.enabled);
      if (enabledBanks[index]) {
        this.postData.bank_id = enabledBanks[index].bank_id;
      }
      this.showPicker = false;
    },

    onSubmit() {
      if (!this.postData.bank_name) {
        this.$Dialog.Toast(this.$t("bankCard.placeholder[0]"));
        return;
      }
      if (!this.postData.card_no) {
        const placeholder = this.isUSDTBank ?
          this.$t("bankCard.placeholder[6]") :
          this.$t("bankCard.placeholder[1]");
        this.$Dialog.Toast(placeholder);
        return;
      }

      this.isSubmit = true;
      this.$Model.AddBankCard(this.postData, (data) => {
        this.isSubmit = false;
        if (data.code == 1) {
          this.showAdd = false;
        }
      });
    },
  },
};
</script>
<style scoped>
.PageBox {
  background-color: white !important;
}
.Site .van-cell {
  background: none !important;
}
.formBox,
.listBox {
  width: calc(100% - 40px);
  margin: 0 auto;
  background-color: #f8f8f8;
  border-radius: 12px;
  margin-top: 20px;
}
.list {
  margin: 10px 0;
}
.list .van-cell__left-icon {
  font-size: 50px;
  color: #4087f1;
  margin-right: 15px;
}
.list .van-cell__label {
  font-size: 16px;
  color: #888;
}
.list .van-cell__title {
  font-size: 16px;
  color: #292929;
}
</style>
