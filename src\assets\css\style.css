.MiLineBtn[data-v-b5fa7c46] {
  position: absolute;
  left: 0;
  z-index: 99;
  color: #fff;
  font-size: 1rem;
  line-height: 3em;
  display: block;
  padding: 0 0.625rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.MiLineBtn[data-v-b5fa7c46]:before {
  content: "\E909";
  font-size: 1.25rem;
  font-family: iconfont;
  vertical-align: middle;
  margin-right: 0.3125rem;
}

.MiLine[data-v-b5fa7c46] {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 99999;
  border: 0.375rem solid rgba(55, 210, 59, 0.35);
  border-radius: 100%;
  width: 3.625rem;
  height: 3.625rem;
  opacity: 1;
  overflow: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.MiLine.open[data-v-b5fa7c46] {
  width: 100%;
  height: 100%;
  border-radius: 0;
  border-width: 0;
  overflow: inherit;
  opacity: 1;
}

.MiLine.touchmove[data-v-b5fa7c46] {
  -webkit-transition-duration: 0s;
  transition-duration: 0s;
}

.MiLineFixedBtn[data-v-b5fa7c46] {
  font-size: 0;
  display: block;
  width: 100%;
  height: 100%;
  background: url(../../static/img/icon-miliao.3dec0a01.svg) no-repeat;
  background-size: cover;
}

.CancelMiLine[data-v-b5fa7c46] {
  position: fixed;
  z-index: 999;
  left: 0;
  right: 0;
  bottom: -100%;
  height: 3.75rem;
  -webkit-transition: bottom 0.2s;
  transition: bottom 0.2s;
  background: -webkit-linear-gradient(right, transparent, #000);
  background: linear-gradient(-90deg, transparent, #000);
  background: -webkit-linear-gradient(-90deg, transparent, #000);
  font-size: 0.875rem;
  color: #aaa;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.CancelMiLine img[data-v-b5fa7c46] {
  margin-right: 0.625rem;
}

.CancelMiLine.open[data-v-b5fa7c46] {
  bottom: 0;
}

.CancelMiLine.on[data-v-b5fa7c46] {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
}

.CancelMiLine.on img[data-v-b5fa7c46] {
  height: 2.5rem;
}

html {
  width: 100%;
  height: 100%;
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

@media screen and (max-width: 320px) {
  html {
    font-size: 87.5%;
  }
}

@media screen and (min-width: 640px) {
  html {
    font-size: 175%;
  }
}

body {
  font-family: Product Sans, Roboto, Helvetica Neue, Helvetica, Tohoma, Arial,
    MicrosoftYaHei, PingFang SC, Hiragino Sans GB, STXihei, Source Han Sans CN,
    Microsoft YaHei UI, Microsoft YaHei, Heiti SC, sans-serif;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
  max-width: 46.875rem;
  height: 100%;
  margin: 0 auto;
  color: #000;
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

marquee {
  margin: 0;
  padding: 0;
  vertical-align: top;
}

*,
:after,
:before {
  box-sizing: border-box;
}

*,
:after,
:before,
p {
  margin: 0;
  padding: 0;
}

em,
i {
  font-style: normal;
}

.Site a {
  color: #4087f1;
}

b {
  font-weight: 600;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
}

h3 {
  font-size: 1.25rem;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

table thead tr {
  background-color: #f2f2f2;
}

select {
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

img {
  display: inline-block;
  vertical-align: top;
}

.bold {
  font-weight: 600;
}

.m0 {
  margin: 0 !important;
}

.mt10 {
  margin-top: 0.625rem !important;
}

.mt15 {
  margin-top: 0.9375rem !important;
}

.mt20 {
  margin-top: 1.25rem !important;
}

.mt30 {
  margin-top: 1.875rem !important;
}

.mr15 {
  margin-right: 0.9375rem !important;
}

.mb10 {
  margin-bottom: 0.625rem !important;
}

.p0 {
  padding: 0 !important;
}

.f13,
.f13 * {
  font-size: 0.8125rem !important;
}

.f14 {
  font-size: 0.875rem !important;
}

.fl {
  float: left !important;
}

.fr {
  float: right !important;
}

.Body,
.Main {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.Main {
  position: relative;
}

.ScrollBox {
  overflow-x: hidden;
  overflow-y: auto;
}

.IndexBox,
.ScrollBox {
  position: relative;
  width: 100%;
  height: 100%;
}

.IndexBox {
  padding-bottom: 3.25rem;
}

.IndexBox,
.PageBox {
  overflow: hidden;
  color: #fff;
}

.PageBox {
  width: 100%;
  height: 100%;
  padding-top: 2.875rem;
  position: relative;
}

.nav-logo {
  height: 1.5rem;
  margin-top: 0.75rem;
  vertical-align: top;
}

.ProjectPanel {
  overflow: hidden;
  width: 100%;
  margin: 0.625rem 0;
  padding: 0 0.125rem;
  border: 0.0625rem solid #e5e5e5;
  border-radius: 0.125rem;
  background-color: #fff;
}

.ProjectPanel > a {
  display: block;
}

.ProjectPanel .title {
  font-size: 1rem;
  padding: 0.625rem;
  border-bottom: 0.0625rem solid #eee;
}

.ProjectPanel .title i {
  font-size: 0.8125rem;
  display: inline-block;
  padding: 0 0.5rem;
  color: #f60;
  border: 0.0625rem solid #f60;
  border-radius: 6.25rem;
}

.ProjectPanel .info {
  padding: 0.625rem 0;
}

.ProjectPanel .info,
.ProjectPanel .info li {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

.ProjectPanel .info li {
  line-height: 1;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  padding: 0.3125rem;
  text-align: center;
  color: #888;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.ProjectPanel .info li + li {
  border-left: 0.0625rem solid #eee;
}

.ProjectPanel .info li > span {
  font-size: 1.5rem;
  margin-top: 0.9375rem;
  color: red;
}

.ProjectPanel .info li > span em {
  font-size: 0.875rem;
  color: #333;
}

.ProjectPanel .info li .van-button {
  font-size: 0.875rem;
  padding: 0 0.9375rem;
  vertical-align: top;
  border-radius: 0.3125rem;
}

.ProjectPanel .footer {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.625rem;
  color: #aaa;
  border-top: 0.0625rem solid #eee;
}

.ProjectPanel .footer span {
  margin-right: 0.9375rem;
}

.FixedButton {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 0.625rem 0.9375rem;
  background-color: #fff;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}

.DataLoad {
  position: absolute;
  z-index: 9;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.user-header {
  width: 3.125rem;
  height: 3.125rem;
  background-color: #f5f5f5;
  border-radius: 100%;
  border: 0.0625rem solid #ddd;
  overflow: hidden;
  margin: 0 auto;
}

.user-header img {
  width: 100%;
  height: 100%;
}

.Login {
  text-align: center;
}

.Login h1 {
  padding: 1.875rem 1rem 1.25rem;
  text-align: left;
  color: #fff;
}

.Login .van-cell {
  font-size: 0.9375rem;
  background: transparent !important;
}

.Login .van-field__label {
  width: 4.6875rem;
}

.Login .van-cell .van-field__left-icon .van-icon {
  font-size: 1.125rem;
  color: #4b34c3;
}

.Login .van-field__control {
  color: #fff;
  font-size: 0.75rem;
}

.Login input::-webkit-input-placeholder {
  color: #888;
  font-size: 0.75rem;
}

.Login .van-checkbox__label,
.Login input::placeholder {
  color: #888;
  font-size: 0.75rem;
}

.Site .van-image-preview__image {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.Site .van-image-preview .van-image__img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}

.Site .van-nav-bar {
  background: transparent;
}

.Site .van-nav-bar__left {
  left: 0;
  padding: 0 0.625rem;
}

.Site .van-nav-bar__right {
  right: 0;
  padding: 0 0.625rem;
}

.Site .van-nav-bar__title {
  font-size: 1.125rem;
  color: #fff;
}

.Site .van-nav-bar__text {
  margin: 0;
  padding: 0;
  color: #bbb;
}

.Site .van-nav-bar .van-icon {
  font-size: 1.5rem;
  color: #bbb;
}

.Site .van-grid-item__text {
  font-size: 0.875rem;
  margin-top: 0.5rem;
  color: #000;
}

.Site .van-tab {
  font-size: 0.9375rem;
}

.Site .van-cell {
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  color: #292929;
}

.Site .van-cell .van-cell__value,
.Site .van-field__control {
  color: #bbb;
}

.Site .van-cell:not(:last-child):after {
  left: 0;
  border-color: #0e1526;
}

.Site .van-radio__label {
  color: #bbb;
}

.Site .van-action-sheet__header {
  color: #fff;
}

.Site .van-skeleton {
  margin: 0.625rem 0;
  padding: 0.625rem;
  background-color: #fff;
}

.Site [class*="van-hairline"]:after {
  border-color: #0e1526;
}

.Site .van-button--mini {
  padding: 0 0.5rem;
}

.Site .van-button {
  border-radius: 0.3125rem;
}

.Site .van-button--danger {
  border-color: transparent;
  background: -webkit-linear-gradient(left, #4b34c3, #3d3ff7);
  background: linear-gradient(90deg, #4b34c3, #3d3ff7);
}

.Site .van-button--plain.van-button--danger {
  background-color: transparent;
  color: #dd6161;
}

.Site .van-button--round {
  border-radius: 6.25rem;
}

.Site .van-button--mini {
  height: 1.625rem;
}

.Site .van-popup--bottom.van-popup--round {
  border-radius: 0.625rem 0.625rem 0 0;
}

.Site .van-divider {
  border-color: hsla(0, 0%, 100%, 0.2);
}

.SiteDialog.van-dialog {
  color: #333;
}

.SiteDialog.van-dialog .van-cell,
.SiteDialog.van-dialog .van-field__control {
  background-color: transparent;
  color: #333;
}

.SiteDialog.van-dialog [class*="van-hairline"]:after {
  border-color: #ebedf0;
}

.van-dialog__message {
  overflow-x: hidden;
}

.SiteToast.van-toast {
  width: auto;
  background-color: hsla(0, 0%, 100%, 0.8);
  color: #333;
}

.Site .van-action-sheet,
.Site .van-calendar,
.Site .van-grid-item__content,
.Site .van-picker {
  background-color: #151d31;
}

.Site .van-picker-column__item {
  color: #fff;
}

.Site .van-picker__mask {
  background-image: -webkit-linear-gradient(
      top,
      rgba(21, 29, 49, 0.9),
      rgba(21, 29, 49, 0.4)
    ),
    -webkit-linear-gradient(bottom, rgba(21, 29, 49, 0.9), rgba(21, 29, 49, 0.4));
  background-image: linear-gradient(
      180deg,
      rgba(21, 29, 49, 0.9),
      rgba(21, 29, 49, 0.4)
    ),
    linear-gradient(0deg, rgba(21, 29, 49, 0.9), rgba(21, 29, 49, 0.4));
}

.Site .van-picker__confirm {
  color: #4087f1;
}

.van-pull-refresh {
  -webkit-user-select: text;
  user-select: text;
}

.Panel {
  width: 100%;
  overflow: hidden;
}

.Panel .title {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  background: url(../../static/img/profile-bg.adb1d316.png) no-repeat;
  background-size: cover;
  padding: 0.625rem;
  font-size: 1rem;
}

.Panel .title .van-icon {
  margin-right: 0.3125rem;
}

.Panel .title label {
  font-size: 0.75rem;
  color: #999;
  text-align: right;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

.NoticePopup {
  background: url(../../static/img/bg_mine.0c92df27.png) no-repeat #052058;
  background-size: contain;
  height: 21.25rem;
  padding: 0 1.5625rem 1.5625rem 1.5625rem;
  border-radius: 0.625rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
}

.NoticePopup dt {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  line-height: 3.125rem;
}

.NoticePopup dd {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0.625rem;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0.625rem;
  color: #ccc;
  -webkit-box-flex: 1;
  -webkit-flex: auto;
  flex: auto;
  text-align: justify;
}

.NoticePopup ~ .close {
  display: inline-block;
  margin-top: 0.3125rem;
  font-size: 0;
}

.TaskItem {
  margin: 0.125rem 0;
  background-color: #151d31;
  -webkit-box-align: start !important;
  -webkit-align-items: flex-start !important;
  align-items: flex-start !important;
}

.TaskItem .icon {
  text-align: center;
  line-height: 1;
}

.TaskItem .icon h4 {
  margin-bottom: 0.5rem;
}

.TaskItem .icon a {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 5px;
  overflow: hidden;
  margin: 0 auto;
}

.TaskItem .icon img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.TaskItem .icon .van-tag {
  margin-top: 0.5rem;
}

.TaskItem .icon .price {
  color: #dd6161;
  margin-top: 0.625rem;
}

.TaskItem .icon .price b {
  font-size: 1.25rem;
}

.TaskItem .van-cell__title {
  text-align: left;
  line-height: 1;
  margin-left: 0.9375rem;
  color: #292929;
  font-size: 12px;
}

.TaskItem .van-cell__title > div {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  color: #ccc;
}

.TaskItem .van-cell__title > div span + span {
  margin-left: 0.625rem;
}

.TaskItem .van-cell__title > div + div {
  margin-top: 0.5rem;
}

.TaskItem .van-cell__title > div i {
  color: #f1c70d;
  font-size: 1rem;
}

.TaskItem .van-cell__title > div b {
  color: #4087f1;
}

.TaskItem .van-cell__title > div em {
  font-weight: 600;
  color: #4087f1;
  font-size: 1.125rem;
}

.TaskItem .record h4 {
  font-size: 0.9375rem;
}

.TaskItem .post > p,
.TaskItem .record > p {
  margin-top: 0.625rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  color: #758ab0;
  font-size: 0.8125rem;
}

.TaskItem .post > p {
  margin-top: 0.625rem;
}

.TaskItem .post > p span + span {
  margin-left: 1.25rem;
}

.TaskItem .post > p em {
  color: #4087f1;
}

.TaskItem .record .href {
  margin-top: 0.9375rem;
}

.TaskItem .record .href > a {
  font-size: 0.75rem;
  display: inline-block;
  color: #758ab0;
}

.TaskItem .record .href > a + a {
  margin-left: 0.625rem;
  border-left: 0.0625rem solid #758ab0;
  padding-left: 0.625rem;
}

.TaskItem .van-cell__value {
  -webkit-box-flex: 0;
  -webkit-flex: none;
  flex: none;
  margin-left: 1.25rem;
  color: #758ab0 !important;
}

.TaskItem .van-cell__value .price {
  color: #758ab0;
  line-height: 1;
  font-size: 0.75rem;
}

.TaskItem .van-cell__value .price p {
  margin-top: 0.3125rem;
  color: #4087f1;
  font-size: 1rem;
}

.TaskItem .van-cell__value b {
  color: #4087f1;
  font-size: 1.25rem;
  font-weight: 400;
}

.TaskItem .audit p {
  margin-bottom: 0.625rem;
  line-height: 1;
}

.TaskItem .van-cell__value.audit .state2,
.TaskItem .van-cell__value .state1 {
  color: #dd6161;
}

.TaskItem .van-cell__value.audit .state3,
.TaskItem .van-cell__value.audit .state4,
.TaskItem .van-cell__value .state2,
.TaskItem .van-cell__value .state5 {
  color: #bbb;
}

.TaskItem .van-cell__value.audit .state1,
.TaskItem .van-cell__value .state3 {
  color: #f1c70d;
}

.TaskItem .button {
  padding: 0.3125rem 1rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  border-top: 0.0625rem solid hsla(0, 0%, 100%, 0.1);
}

.Empty .van-list__finished-text:before {
  content: "";
  height: 8rem;
  display: block;
  background: url(../../static/img/no_data.7d5de337.png) no-repeat center 0;
  background-size: contain;
  margin-top: 40%;
  opacity: 0.3;
}

.FundItem {
  margin: 0.75rem 0;
  padding: 0.625rem;
  line-height: 1;
}

.FundItem .icon {
  background-color: #dd6161;
  color: #fff;
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
  border-radius: 100%;
  font-size: 0.75rem;
  margin-right: 0.625rem;
  -webkit-box-flex: 0;
  -webkit-flex: none;
  flex: none;
}

.FundItem .tag1 {
  background-color: #07c160;
}

.FundItem .tag2 {
  background-color: #1989fa;
}

.FundItem .van-cell__title {
  overflow: hidden;
}

.FundItem .van-cell__title > div {
  color: #c0c4cc;
  font-size: 0.8125rem;
  white-space: nowrap;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.FundItem .van-cell__title > div span {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

.FundItem .van-cell__title > div span:last-child {
  text-align: right;
}

.FundItem .van-cell__title > div:first-child {
  color: #c0c4cc;
}

.FundItem .van-cell__title > div:last-child {
  color: #999;
  margin-top: 0.5rem;
}

.FundItem .van-cell__title .money {
  font-size: 1.125rem;
  font-weight: 600;
  color: #4087f1;
}

.MyEarnings .van-grid-item {
  overflow: hidden;
}

.MyEarnings .van-grid-item__content {
  color: #4087f1;
  font-size: 1.125rem;
  text-align: center;
  background-color: #151d31;
}

.MyEarnings .van-grid-item__icon-wrapper {
  color: #bbb;
  margin-bottom: 0.3125rem;
  font-size: 0.875rem;
}

#Service {
  position: absolute;
  bottom: 3.5rem;
  right: 0;
  z-index: 99;
  color: #999;
  line-height: 1;
  text-align: center;
  font-size: 0.75rem;
}

#Service img {
  display: block;
  margin: 0 auto;
}

#Service.move {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

#Turntable {
  position: absolute;
  bottom: 8.125rem;
  right: 0;
  z-index: 99;
  color: #999;
  line-height: 1;
  text-align: center;
  font-size: 0.75rem;
}

#Turntable img {
  display: block;
  margin: 0 auto;
}

#Turntable.move {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.MyTask {
  background-color: #fff;
  border: 0.0625rem solid #eee;
  border-radius: 0.3125rem;
  overflow: hidden;
  margin: 0.625rem 0;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
  position: relative;
}

.MyTask dt {
  background: url(../../static/img/profile-bg.adb1d316.png) no-repeat;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  padding: 0.5rem 0.625rem;
  line-height: 1;
}

.MyTask dt img {
  vertical-align: middle;
}

.MyTask dt .van-icon {
  color: #999;
  font-size: 1.25rem;
}

.MyTask dd {
  padding: 0 0.625rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  margin: 0.625rem 0;
}

.MyTask dd > div p + p {
  margin-top: 0.625rem;
}

.MyTask dd .money {
  font-size: 1rem;
}

.MyTask dd .money b {
  font-size: 2rem;
}

.MyTask dd.state {
  position: absolute;
  right: 0;
  top: 4.0625rem;
  z-index: 2;
}

.Loading {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.van-tabbar[data-v-5f62195b] {
  height: 3.25rem;
  background: #0e1526;
}

.van-tabbar-item__icon[data-v-5f62195b] {
  font-size: 1.5rem;
}

.van-tabbar-item__icon img[data-v-5f62195b] {
  height: 1.5rem;
}

.van-tabbar-item__icon .addTask[data-v-5f62195b] {
  height: 4.5rem;
  margin: -2.375rem 0 -0.625rem;
}

.van-tabbar-item--active[data-v-5f62195b] {
  background: -webkit-linear-gradient(left, #2ddaff, #0049e7);
  background: linear-gradient(90deg, #2ddaff, #0049e7);
  -webkit-background-clip: text;
}

.loginBtn {
  background: #FF0F23 !important;
  border-radius: 12px !important;
  border: 1px solid #FF0F23 !important;
}

.PickerPopup {
  background-color: white;
  padding: 16px;
  margin-top: 20px;
  background: #fff url("../../../static/images/NoticePopup.png") no-repeat;
  background-size: 100%;
  border-radius: 32px 32px 0 0 !important;
}
.PickerPopup .van-picker {
  background-color: transparent;
}
.PickerPopup .van-picker__mask {
  background: none;
}
.PickerPopup .van-picker-column__item {
  font-size: 0.8rem;
  color: #2c2c2c;
}
.PickerPopup .van-picker__confirm,
.PickerPopup .van-picker__cancel {
  color: white;
}

.DrawPopup {
  background-color: #fff !important;
  background: url("../../../static/images/NoticePopup.png") no-repeat;
  background-size: 100%;
  border-radius: 32px 32px 0 0 !important;
}
.DrawPopup .van-action-sheet__header {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.DrawPopup >>> .van-cell {
  background: transparent;
}
.DrawPopup >>> .van-field__label {
  font-size: 0.8rem;
  color: #7d7d7d;
}
.DrawPopup >>> .van-field__control {
  font-size: 0.8rem;
  color: #2c2c2c;
}
.DrawPopupBox {
  width: calc(100% - 40px);
  margin: 0 auto;
  background-color: #f8f8f8;
  border-radius: 12px;
  padding: 16px;
  margin-top: 20px;
}
