<template>
	<div class="IndexBox">
		<view>
			<p>{{$t('baozhengjin')}}:{{UserInfo.baozhengjin}}</p>
		</view>
		<div style="padding: 16px;">
		  <van-button type="danger" block style="font-size: 16px" to="/serviceCenter">{{$t('unbaozhengjin')}}</van-button>
		</div>
		<Footer v-if="$route.name=='user'" />
	</div>
</template>

<script>
	export default {
	  name: 'baozhengjin',
	  components: {
	  },
	  props: [],
	  data() {
	    return {
	      statisticalData: {}
	    }
	  },
	  computed: {
	
	  },
	  watch: {
	
	  },
	  created() {
	    // 用户信息已在路由守卫或App.vue中获取，无需重复获取
	    // this.$Model.GetUserInfo()
	    this.$Model.GetStatisticsInfo(data=>{
	      if(data.code==1){
	        this.statisticalData = data.info
	      }
	    })
	  },
	  mounted() {
	
	  },
	  activated() {
	
	  },
	  destroyed() {
	
	  },
	  methods: {
	    unbaozhengjin(){
			//跳转客服
			
		}
	  }
	}
</script>

<style>
</style>