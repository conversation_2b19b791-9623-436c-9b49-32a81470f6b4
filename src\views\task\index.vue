<template>
  <div class="Site IndexBox">
    <div class="ScrollBox">
      <van-swipe
        class="customSwipe"
        :autoplay="3000"
        indicator-color="#888"
        style="height: 10rem"
      >
        <van-swipe-item
          v-for="(item, index) in InitData.bannerList"
          :key="index"
        >
          <img :src="InitData.setting.up_url + item" width="100%" />
        </van-swipe-item>
      </van-swipe>
      <van-tabs
        :ellipsis="false"
        :border="false"
        class="customTabs"
        color="#4087f1"
        title-active-color="#fff"
        title-inactive-color="#292929"
        line-width="0"
        v-model="tabsIndex"
        @change="changeTabs"
      >
        <van-tab
          v-for="item in InitData.taskclasslist.filter((obj) => obj.state == 1)"
          :title="item.group_name"
          :key="item.group_id"
        >
          <van-pull-refresh v-model="isRefresh" @refresh="onRefresh">
            <van-list
              v-model="isLoad"
              :finished="isFinished"
              :finished-text="
                listData.length ? $t('vanPull[0]') : $t('vanPull[1]')
              "
              @load="onLoad"
              :class="{ Empty: !listData.length }"
            >
              <van-cell
                class="TaskItem"
                :border="false"
                v-for="item in listData"
                :key="InitData.setting.up_url + item.task_id"
                :to="`/taskShow/${item.task_id}`"
              >
                <div class="icon" slot="icon">
                  <h4>{{ item.group_name }}</h4>
                  <a href="javascript:;">
                    <img :src="InitData.setting.up_url + item.icon" />
                  </a>
                  <van-tag type="primary">{{ item.vip_dec }}</van-tag>
                </div>
                <template #title>
                  <div>
                    <span
                      >{{ $t("task.list[2]") }}:<b>{{
                        item.surplus_number
                      }}</b></span
                    >
                    <span>
                      {{ InitData.currency }}
                      <em>{{ Number(item.reward_price) }}</em>
                    </span>
                  </div>
                  <div>
                    <span>{{ $t("task.list[3]") }}:{{ item.group_info }}</span>
                    <span
                      ><van-button
                        type="info"
                        size="mini"
                        @click.stop="receiveTask(item.task_id, item)"
                        :disabled="item.is_l == 0 ? false : true"
                        >{{ $t("task.list[4]") }}</van-button
                      ></span
                    >
                  </div>
                </template>
              </van-cell>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>
    <Footer />
  </div>
</template>

<script>
export default {
  name: "Task",
  components: {},
  props: ["tabsActive"],
  data() {
    return {
      taskType: "",
      tabsIndex: 0,
      isStore: false,
      listData: "",
      isLoad: false,
      isFinished: false,
      isRefresh: false,
      pageNo: 1,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.tabsIndex = this.tabsActive ? Number(this.tabsActive) : 0;
    if (this.InitData.taskclasslist.length) {
      this.taskType = this.InitData.taskclasslist.filter(
        (obj) => obj.state == 1
      )[0].group_id;
      if (this.tabsActive) {
        this.taskType = this.InitData.taskclasslist.filter(
          (obj) => obj.state == 1
        )[this.tabsActive].group_id;
      }
    }
    this.getListData("init");
  },

  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    onLoad() {
      this.getListData("load");
    },
    changeTabs(index) {
      this.taskType = this.InitData.taskclasslist.filter(
        (obj) => obj.state == 1
      )[index].group_id;
      this.getListData("init");
    },
    openTaskList(grade, name) {
      if (this.UserInfo.vip_level < grade) {
        const html = this.$t("task.index[1]", {
          currVip: this.UserInfo.useridentity,
          vip: name,
        });
        this.$Dialog.Confirm(
          html,
          () => {
            this.$router.push("/vip");
          },
          this.$t("task.index[2]")
        );
      } else {
        if (this.taskType) {
          this.$router.push(`/taskList/${this.taskType}/${grade}`);
        } else {
          this.$Dialog.Toast(this.$t("task.index[3]"));
        }
      }
    },
    getListData(type) {
      this.isLoad = true;
      this.isRefresh = false;
      if (type == "load") {
        this.pageNo += 1;
      } else {
        this.pageNo = 1;
        this.isFinished = false;
      }
      this.$Model.GetTaskList(
        {
          group_id: this.taskType,
          page_no: this.pageNo,
          is_u: 0
        },
        (data) => {
          this.$nextTick(() => {
            this.isLoad = false;
          });
          if (data.code == 1) {
            if (type == "load") {
              if (this.pageNo == 1) {
                this.listData = data.info;
              } else {
                this.listData = this.listData.concat(data.info);
              }
            } else {
              this.listData = data.info;
            }
            if (this.pageNo == data.data_total_page) {
              this.isFinished = true;
            } else {
              this.isFinished = false;
            }
          } else {
            this.listData = "";
            this.isFinished = true;
          }
        }
      );
    },
    onRefresh() {
      this.getListData("init");
    },
    receiveTask(id, item) {
      if (!localStorage["Token"]) {
        this.$router.push("/login");
      } else {
        this.$Model.ReceiveTask(id, (data) => {
          if (data.code == 1) {
            item.is_l = 1;
            this.pageNo = 0;
          }
        });
      }
    },
  },
};
</script>
<style scoped>
.customTabs {
  margin-top: 20px;
}
.customSwipe {
  border-radius: 8px;
}
.ScrollBox {
  padding: 20px;
  box-sizing: border-box;
}
.van-tab__pane {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.van-cell {
  background-size: contain;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  padding: 20px 15px;
  margin-top: 18px;
  width: 100%;
  align-items: center !important;
}

.van-cell .icon {
  min-width: 60px;
}
.van-cell .van-button--disabled {
  background: #888 !important;
  border-color: #888 !important;
}
/* .van-cell:nth-of-type(2) {
  background: url("../../../static/images/vip_new_bg.png") no-repeat 40px,
    linear-gradient(60deg, #fd9e02, #d86a25);
  background-size: contain;
}
.van-cell:nth-of-type(3) {
  background: url("../../../static/images/vip_new_bg.png") no-repeat 40px,
    linear-gradient(60deg, #3044b3, #273580);
  background-size: contain;
}
.van-cell:nth-of-type(4) {
  background: url("../../../static/images/vip_new_bg.png") no-repeat 40px,
    linear-gradient(60deg, #31b489, #3e91b8);
  background-size: contain;
}
.van-cell:nth-of-type(5) {
  background: url("../../../static/images/vip_new_bg.png") no-repeat 40px,
    linear-gradient(60deg, #47484c, #1f2025);
  background-size: contain;
} */
.van-cell .van-cell__title {
  flex: auto;
  color: #fff;
  text-align: center;
  font-size: 14px;
}

.van-cell .van-cell__value {
  position: absolute;
  top: 10px;
  left: 0;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 12px;
  padding: 0 8px 0 5px;
  border-radius: 0 50px 50px 0;
}
.van-pull-refresh {
  min-height: 100%;
  width: 100%;
}
.Site >>> .van-tab--active {
  background-color: #FF0F23;
  color: #fff;
  border-radius: 50px;
  padding: 0 10px;
  width: 70px;
}
.TaskItem {
  margin: 0 auto;
  border-radius: 12px;
  background-color: #f8f8f8 !important;
}

.TaskItem .van-cell__title div {
  color: #292929;
}

.TaskItem:not(:first-child) {
  margin-top: 10px;
}
.Site >>> .van-button__text {
  color: #fff;
}
.TaskItem .van-cell__title span em {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 700;
  font-style: normal;
  text-shadow: 0 1px 2px rgba(255, 107, 53, 0.3);
}
</style>
