<template>
  <div class="Site PageBox" style="padding: 0;">
    <van-nav-bar
      fixed
      :border="false"
      left-arrow
      @click-left="$router.push('/')"
      @click-right="$langList ? $router.push('/language') : false"
    >
      <template #right v-if="$langList">
        <svg style="color: #333; width: 18px; height: 18px;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <path d="M511.777943 68.191078c-245.204631 0-443.586864 198.60429-443.586864 443.808922s198.382233 443.808922 443.586864 443.808922 444.030979-198.60429 444.030979-443.808922S756.982574 68.191078 511.777943 68.191078zM819.11568 334.476841 688.191838 334.476841c-14.423501-55.476499-34.617425-108.733447-61.245899-157.995407C708.606797 204.440206 776.509303 261.025968 819.11568 334.476841zM512 158.506037c37.058011 53.256948 65.906036 112.505353 84.767616 175.96978L427.232384 334.475817C446.093964 271.01139 474.941989 211.762985 512 158.506037zM168.491459 600.76158c-7.322779-28.40391-11.538801-58.139142-11.538801-88.76158s4.216022-60.35767 11.538801-88.76158l149.785421 0c-3.550873 29.069059-5.991458 58.582233-5.991458 88.76158s2.440585 59.69252 6.213515 88.76158L168.491459 600.76158zM204.662263 689.523159l130.923842 0c14.423501 55.476499 34.617425 108.733447 61.245899 158.217465C315.171146 819.780829 247.267617 762.974032 204.662263 689.523159zM335.586105 334.476841 204.662263 334.476841c42.605354-73.449849 110.508883-130.257669 192.168718-158.217465C370.202507 225.743394 350.009605 279.000342 335.586105 334.476841zM512 865.493963c-36.835953-53.256948-65.683978-112.505353-84.767616-175.96978l169.535231 0C577.682955 752.987586 548.835953 812.235992 512 865.493963zM615.851253 600.76158 408.148747 600.76158c-4.216022-29-069059-7.100722-58.582233-7.100722-88.76158s2.8847-59.69252 7.100722-88.76158l207.702506 0c4.216022 29.069059 7.100722 58.582233 7.100722 88.76158S620.067274 571.69252 615.851253 600.76158zM627.167996 847.51959c26.628474-49.485041 46.821375-102.519931 61.245899-157.995407l130.923842 0C776.510326 762.974032 708.606797 819.558771 627.167996 847.51959zM705.500039 600.76158c3.550873-29.069059 6.213515-58.582233 6.213515-88.76158s-2.440585-59.69252-6.213515-88.76158l149.785421 0c7.322779 28.40391 11.760858 58.139142 11.760858 88.76158s-4.216022 60.35767-11.760858 88.76158L705.500039 600.76158z" fill="currentColor"/>
        </svg>
      </template>
    </van-nav-bar>
    <div class="Login ScrollBox">
      <img class="LoginImgClass" src="@/static/images/loginImg.png" />
      <div class="login-Text">
        {{ $t("login.text[3]") }}
      </div>
      <van-form @submit="onSubmit" class="mt15">
        <div class="loginInputBox">
          <van-field
            :border="false"
            left-icon="manager"
            :placeholder="$t('login.placeholder[0]')"
            size="large"
            autocomplete="off"
          >
            <template #left-icon>
              <van-image
                width="24"
                height="24"
                style="margin-right: 8px;margin-top: 5px;"
                src="./static/icon/loginUserIcon.png"
              />
            </template>
            <template #input>
              <van-dropdown-menu>
                <van-dropdown-item :title="`+${areaCode}`" ref="DropdownItem">
                  <template>
                    <ul>
                      <li
                        v-for="(item, index) in areaList"
                        :key="index"
                        :class="{ on: areaCode == item.id }"
                        @click="
                          (areaCode = item.id), $refs.DropdownItem.toggle()
                        "
                      >
                        +{{ item.id }}&#12288;{{ item.name }}
                      </li>
                    </ul>
                  </template>
                </van-dropdown-item>
              </van-dropdown-menu>
              <input
                type="tel"
                v-model.trim="postData.username"
                :placeholder="$t('login.placeholder[0]')"
                style="border:0;flex:1;width:100px;padding-left: 50px;background: transparent"
              />
            </template>
          </van-field>
        </div>
        <div class="loginInputBox">
          <van-field
            :border="false"
            class="customVantCell"
            v-model.trim="postData.password"
            :type="showPass ? 'text' : 'password'"
            :placeholder="$t('login.placeholder[1]')"
            size="large"
            clearable
            autocomplete="off"
            :right-icon="showPass ? 'eye' : 'closed-eye'"
            @click-right-icon="showPass = !showPass"
          >
            <template #left-icon>
              <van-image
                width="24"
                height="24"
                style="margin-right: 8px;margin-top: 5px;"
                src="./static/icon/loginPaswordIcon.png"
              />
            </template>
          </van-field>
        </div>
        <van-cell size="large" :border="false">
          <van-checkbox
            style="margin-left: 22px;"
            v-model="keepUser"
            @change="changeKeepUser"
            checked-color="#FF0F23"
            icon-size="16"
            >{{ $t("login.text[0]") }}</van-checkbox
          >
        </van-cell>

        <div style="padding: 20px 16px">
          <van-button
            size="large"
            round
            block
            type="danger"
            :loading="isSubmit"
            :loading-text="$t('login.text[1]')"
            style="font-size: 18px;"
            class="loginBtn"
            >{{ $t("login.text[2]") }}</van-button
          >
          <i18n
            path="login.i18n[0]"
            tag="div"
            style="text-align: center;color: #7D7D7D;margin-top: 30px"
          >
            <template #title>{{ InitData.setting && InitData.setting.web_title || 'SmartNest' }}</template>
            <router-link class="href" slot="a" to="/register">{{
              $t("login.i18n[1]")
            }}</router-link>
            <!-- <router-link class="href fr" slot="line" to="/line">{{
              $t("line")
            }}</router-link> -->
          </i18n>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "Login",
  components: {},
  props: [],
  data() {
    return {
      postData: {
        username: localStorage["UserName"] || "",
        password: localStorage["PassWord"] || "",
      },
      keepUser: localStorage["KeepUser"] == "1" ? true : false,
      isSubmit: false,
      showPass: false,
      docTitle: document.title,
      areaList: areaList,
      areaCode: '62',
    };
  },
  computed: {},
  watch: {},
  created() {
    this.$Model.SmsCode((data) => {
      this.areaList = data;
      // 优先选择62区号，如果不存在则选择第一个
      const area62 = data.find(item => item.id == '62');
      this.areaCode = area62 ? '62' : data[0].id;
    });
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    onSubmit() {
      if (!this.postData.username) {
        this.$Dialog.Toast(this.$t("login.placeholder[0]"));
        return;
      }
      if (!this.postData.password) {
        this.$Dialog.Toast(this.$t("login.placeholder[1]"));
        return;
      }
      this.isSubmit = true;
      this.$Model.Login(this.postData, (data) => {
        this.isSubmit = false;
        if (this.keepUser) {
          localStorage["UserName"] = this.postData.username;
          localStorage["PassWord"] = this.postData.password;
        } else {
          localStorage.removeItem("UserName");
          localStorage.removeItem("PassWord");
        }
        if (data.code == 1) {
          this.$parent.clearMiliao();
        }
      });
    },
    changeKeepUser() {
      if (this.keepUser) {
        localStorage["KeepUser"] = 1;
      } else {
        localStorage.removeItem("KeepUser");
      }
    },
  },
};
</script>
<style scoped>
.PageBox {
  background: white;
}
.PageBox .LoginImgClass{
  width: 80%;
  margin-top: 30px;
}
.van-dropdown-menu {
  position: absolute;
  left: 0;
}
.van-dropdown-menu >>> .van-dropdown-menu__bar {
  height: auto;
  box-shadow: none;
  background: transparent;
}
.van-dropdown-menu >>> .van-dropdown-menu__title {
  padding: 0 10px 0 0;
  margin-right: 10px;
  color: #888;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul {
  padding: 0 15px;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul li {
  padding: 8px 0;
  width: 100%;
  font-size: 14px;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul li.on {
  color: #1989fa;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul li + li {
  border-top: 1px #f5f5f5 solid;
  color: #333;
}
.login-title {
  font-size: 46px;
  font-weight: 600;
  color: #292929;
  margin-top: 40px;
}
.login-Text {
  font-size: 24px;
  color: #292929;
  text-align: left;
  font-weight: 600;
  margin-top: 10px;
  text-indent: 20px;
}
.loginInputBox {
  width: calc(100% - 40px);
  height: 60px;
  border-radius: 12px;
  background: #f8f8f8;
  margin: 0 auto;
  &:not(:first-child) {
    margin-top: 20px;
  }
}
.loginInputBox >>> .van-field {
  display: flex;
  align-items: center;
}
.loginInputBox >>> .van-dropdown-menu__title::after {
  border: 5px solid;
  border-color: transparent transparent #5a5a68 #5a5a68;
  margin-top: -8px;
  right: -7px;
}
.loginInputBox >>> input {
  font-size: 16px;
  color: #7d7d7d;
}
.href {
  color: #FF0F23;
  font-weight: bold;
}

</style>
