<template>
  <div class="PageBox">
    <div class="ScrollBox">
      <div class="Robot">
        <h2>{{$t('user.robot[1]')}}</h2>
        <p>{{$t('user.robot[2]')}}</p>
        <p>{{$t('user.robot[3]')}}</p>
        <van-button class="mt15" type="danger" block style="font-size: 16px;" :disabled="UserInfo.is_housekeeper==1?true:false" @click="onSubmit">{{$t('user.robot[4]')}}</van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Robot',
  components: {
  },
  props: [],
  data() {
    return {

    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.$parent.navBarTitle = this.$t('user.robot[0]')
  },
  mounted() {

  },
  activated() {

  },
  destroyed() {

  },
  methods: {
    onSubmit() {
      this.$Model.SetUserInfo({is_housekeeper:1})
    }
  }
}
</script>
<style scoped>
.Robot{
  padding: 16px;
}
.Robot h2{
  text-align: center;
  margin-bottom: 10px;
}
.Robot p{
  margin-top: 5px;
}
</style>
