const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 配置
const SRC_DIR = path.resolve(__dirname, '../src');
const I18N_DIR = path.resolve(__dirname, '../src/i18n');

// 自动读取语言文件列表
function getLangFiles() {
  try {
    // 读取i18n目录下的所有js文件
    const files = fs.readdirSync(I18N_DIR)
      .filter(file => file.endsWith('.js') && !file.includes('index.js') && !file.includes('locale/'));

    console.log(`自动检测到 ${files.length} 个语言文件: ${files.join(', ')}`);
    return files;
  } catch (err) {
    console.error('读取语言文件列表失败:', err);
    // 如果读取失败，返回默认列表
    return ['cn.js', 'en.js', 'vi.js', 'th.js', 'id.js', 'yd.js', 'es.js', 'jp.js', 'ma.js', 'pt.js', 'ft.js'];
  }
}

const LANG_FILES = getLangFiles();

// 存储所有语言文件的内容
const langData = {};

// 存储所有找到的翻译键
const foundKeys = new Set();

// 存储缺失的翻译键
const missingTranslations = {};

// 初始化缺失翻译对象
LANG_FILES.forEach(file => {
  const langCode = file.replace('.js', '');
  missingTranslations[langCode] = [];
});

// 更准确地解析JavaScript对象并提取所有键
function extractKeysFromLangFile(content) {
  const keys = new Set();

  try {
    // 移除注释
    let cleanContent = content.replace(/\/\*[\s\S]*?\*\//g, '');
    cleanContent = cleanContent.replace(/\/\/.*$/gm, '');

    // 提取 export default {...} 中的内容
    const match = cleanContent.match(/export\s+default\s+(\{[\s\S]*\});?\s*$/);
    if (!match || !match[1]) {
      console.error('无法找到 export default 语句');
      return keys;
    }

    // 使用 eval 解析对象 (仅用于分析，不推荐在生产环境中使用)
    const objStr = `(${match[1]})`;
    const langObj = eval(objStr);

    // 递归提取所有键路径
    function extractAllPaths(obj, prefix = '') {
      for (const key in obj) {
        const currentPath = prefix ? `${prefix}.${key}` : key;
        keys.add(currentPath);

        // 处理数组
        if (Array.isArray(obj[key])) {
          for (let i = 0; i < obj[key].length; i++) {
            // 添加数组索引键
            const arrayPath = `${currentPath}[${i}]`;
            keys.add(arrayPath);

            // 如果数组元素是对象，递归处理对象的属性
            if (obj[key][i] && typeof obj[key][i] === 'object') {
              for (const prop in obj[key][i]) {
                const propPath = `${arrayPath}.${prop}`;
                keys.add(propPath);
              }
            }
          }
        }

        // 递归处理嵌套对象
        if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
          extractAllPaths(obj[key], currentPath);
        }
      }
    }

    extractAllPaths(langObj);

  } catch (err) {
    console.error('解析语言文件失败:', err);
  }

  return keys;
}



// 加载所有语言文件
function loadLanguageFiles() {
  console.log('正在加载语言文件...');

  LANG_FILES.forEach(file => {
    const filePath = path.join(I18N_DIR, file);
    try {
      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');
      const langCode = file.replace('.js', '');

      // 提取所有键
      const keys = extractKeysFromLangFile(content);
      langData[langCode] = keys;
      console.log(`已加载 ${langCode} 语言文件，共 ${keys.size} 个键`);
    } catch (err) {
      console.error(`无法读取 ${file}:`, err);
    }
  });
}

// 检查键是否存在于语言文件的键集合中
function keyExistsInLang(key, langKeys) {
  return langKeys.has(key);
}

// 扫描文件中的 $t() 调用
function scanFiles() {
  console.log('正在扫描文件中的多语言函数调用...');

  // 获取所有 Vue 和 JS 文件
  const files = glob.sync('**/*.{vue,js}', { cwd: SRC_DIR });

  files.forEach(file => {
    const filePath = path.join(SRC_DIR, file);
    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // 匹配 $t('key') 或 $t("key") 模式
      const regex = /\$t\(['"]([^'"]+)['"]\)/g;
      let match;

      while ((match = regex.exec(content)) !== null) {
        const key = match[1];
        foundKeys.add(key);

        // 如果键包含参数，例如 'home.broadcast'，也添加不带参数的键
        if (key.includes('{')) {
          const baseKey = key.replace(/\{[^}]+\}/g, '');
          if (baseKey !== key) {
            foundKeys.add(baseKey);
          }
        }
      }
    } catch (err) {
      console.error(`无法读取 ${file}:`, err);
    }
  });

  console.log(`共找到 ${foundKeys.size} 个多语言键`);
}

// 检查所有语言文件中是否存在这些键
function checkKeysInLangFiles() {
  console.log('正在检查多语言键是否在语言文件中存在...');
  
  foundKeys.forEach(key => {
    LANG_FILES.forEach(file => {
      const langCode = file.replace('.js', '');
      const langObj = langData[langCode];
      
      if (langObj && !keyExistsInLang(key, langObj)) {
        missingTranslations[langCode].push(key);
      }
    });
  });
}

// 生成报告
function generateReport() {
  console.log('\n===== 多语言检查报告 =====\n');
  
  let hasMissing = false;
  
  Object.keys(missingTranslations).forEach(langCode => {
    const missing = missingTranslations[langCode];
    
    if (missing.length > 0) {
      hasMissing = true;
      console.log(`${langCode}.js 缺少 ${missing.length} 个翻译:`);
      missing.forEach(key => {
        console.log(`  - ${key}`);
      });
      console.log('');
    }
  });
  
  if (!hasMissing) {
    console.log('恭喜！所有多语言键都已在所有语言文件中定义。');
  }
  
  console.log('\n===== 报告结束 =====');
}

// 主函数
function main() {
  console.log('开始检查多语言文件...\n');
  
  // 1. 加载所有语言文件
  loadLanguageFiles();
  
  // 2. 扫描文件中的 $t() 调用
  scanFiles();
  
  // 3. 检查所有语言文件中是否存在这些键
  checkKeysInLangFiles();
  
  // 4. 生成报告
  generateReport();
}

// 运行主函数
main();
