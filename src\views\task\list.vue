<template>
  <div class="Site PageBox">
    <van-nav-bar
      fixed
      :border="false"
      :title="$t('task.list[0]')"
      left-arrow
      @click-left="$router.go(-1)"
    />
    <div class="ScrollBox">
      <van-pull-refresh v-model="isRefresh" @refresh="onRefresh">
        <van-list
          v-model="isLoad"
          :finished="isFinished"
          :finished-text="listData.length?$t('vanPull[0]'):$t('vanPull[1]')"
          @load="onLoad"
          :class="{Empty:!listData.length}"
        >
          <van-cell class="TaskItem" :border="false" v-for="item in listData" :key="InitData.setting.up_url+item.task_id" :to="`/taskShow/${item.task_id}`">
            <div class="icon" slot="icon">
              <h4>{{item.group_name}}</h4>
              <a href="javascript:;">
                <img :src="InitData.setting.up_url+item.icon">
              </a>
              <van-tag type="primary">{{item.vip_dec}}</van-tag>
            </div>
            <template #title>
              <div>
                <span>{{$t('task.list[1]')}}:{{item.username}}</span>
                <i>{{item.status_dec}}</i>
              </div>
              <div>
                <span>{{$t('task.list[2]')}}:<b>{{item.surplus_number}}</b></span>
                <span>
                  {{InitData.currency}}
                  <em>{{Number(item.reward_price)}}</em>
                </span>
              </div>
              <div>
                <span>{{$t('task.list[3]')}}:{{item.group_info}}</span>
                <span><van-button type="info" size="mini" @click.stop="receiveTask(item.task_id,item)" :disabled="item.is_l==0?false:true">{{$t('task.list[4]')}}</van-button></span>
              </div>
            </template>
          </van-cell>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskList',
  components: {
  },
  props: ['taskType','taskGrade'],
  data() {
    return {
      listData: '',
      isLoad: false,
      isFinished: false,
      isRefresh: false,
      pageNo: 1,
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.getListData('init')
  },
  mounted() {
    
  },
  activated() {

  },
  destroyed() {
    
  },
  methods: {
    onLoad() {
      this.getListData('load')
    },
    getListData(type) {
      this.isLoad = true
      this.isRefresh = false
      if(type=='load'){
        this.pageNo+=1
      }else{
        this.pageNo = 1
        this.isFinished = false
      }
      this.$Model.GetTaskList({group_id: this.taskType,page_no: this.pageNo,is_u: 0},data=>{
        this.$nextTick(()=>{
          this.isLoad = false
        })
        if(data.code==1){
          if(type=='load'){
            if(this.pageNo==1){
              this.listData = data.info
            }else{
              this.listData = this.listData.concat(data.info)
            }
          }else{
            this.listData = data.info
          }
          if(this.pageNo==data.data_total_page){
            this.isFinished = true
          }else{
            this.isFinished = false
          }
        }else{
          this.listData = ''
          this.isFinished = true
        }
      })
    },
    onRefresh() {
      this.getListData('init')
    },
    receiveTask(id,item) {
      if(!localStorage['Token']){
        this.$router.push('/login')
      }else{
        this.$Model.ReceiveTask(id,data=>{
          if(data.code==1){
            item.is_l = 1
            this.pageNo = 0
          }
        })
      }
    },
  }
}
</script>
<style scoped>
.van-tabs>>>.van-tabs__wrap{
  height: 55px;
}
.van-tabs>>>.van-tabs__nav--line{
  padding-bottom: 6px;
}
.van-list>>>.TaskItem{
  align-items: center!important;
}
.van-list>>>.TaskItem .icon{
  min-width: 60px;
}
.van-list>>>.TaskItem .van-button--disabled{
  background: #888!important;
  border-color: #888!important;
}
.van-pull-refresh{
  min-height: calc(100vh - 46px);
}
</style>