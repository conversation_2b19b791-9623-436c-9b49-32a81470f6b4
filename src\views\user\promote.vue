<template>
  <div class="invite-page">
    <!-- 顶部导航栏 -->
    <div class="header-nav">
      <div class="back-button" @click="$router.go(-1)">
        <van-icon name="arrow-left" />
      </div>
      <div class="header-title">{{ $t('user.myPage.inviteFriends') }}</div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 顶部装饰背景 -->
      <div class="invite-hero">
        <div class="hero-background">
          <div class="hero-content">
            <!-- 使用提供的背景图片 - 直接相对定位 -->
            <img src="@/static/images/Group 427320209.png" :alt="$t('invite.inviteFriends')" class="hero-image" />
          </div>
        </div>
      </div>

      <!-- 二维码和邀请码白色卡片 - 左右布局 -->
      <div class="qr-invite-card">
        <div class="qr-invite-section">
          <!-- 左侧二维码 -->
          <div class="qr-code-area">
            <div id="QRCode" class="qr-code"></div>
          </div>

          <!-- 右侧邀请码红色区域 -->
          <div class="invite-code-area">
            <div class="invite-code-label">{{ $t('invite.yourInviteCode') }}</div>
            <div id="IosCode" class="invite-code-value">{{ UserInfo.idcode }}</div>
            <van-button
              class="copy-code-button"
              size="small"
              @click="copyInviteCode"
            >
              {{ $t('invite.copyInviteCode') }}
            </van-button>
          </div>
        </div>
      </div>

      <!-- 邀请链接白色卡片 -->
      <div class="link-card">
        <div class="link-label">{{ $t('invite.inviteLink') }}</div>
        <div class="link-input-container">
          <div id="IosLink" class="link-input">{{ promoteUrl }}</div>
          <van-icon
            name="description"
            class="copy-icon"
            @click="copyInviteLink"
            :title="$t('invite.copyLink')"
          />
        </div>
      </div>

      <!-- 分享按钮 -->
      <van-button
        class="share-button"
        type="primary"
        block
        @click="shareInvite"
      >
        <img src="@/static/images/share-icon.svg" class="share-icon" :alt="$t('invite.share')" />
        {{ $t('invite.shareInviteNow') }}
      </van-button>
    </div>

    <!-- 隐藏的输入框用于复制 -->
    <input id="AppCode" type="text" readonly :value="UserInfo.idcode" style="position: absolute;opacity: 0">
    <input id="AppLink" type="text" readonly :value="promoteUrl" style="position: absolute;opacity: 0">
  </div>
</template>

<script>
import QRCode from "qrcodejs2"
export default {
  name: 'Promote',
  components: {
  },
  props: ['isReward'],
  data() {
    return {
      promoteUrl: '',
      saveN: 0,
      showReward: false,
      docTitle: document.title,
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    if(this.isReward){
      this.showReward = true
    }
    this.$parent.navBarTitle = ''
    this.promoteUrl = `${this.InitData.setting.reg_url}/#/register/${this.UserInfo.idcode}`
  },
  mounted() {
    // 延迟生成二维码，确保DOM已渲染
    this.$nextTick(() => {
      const qrElement = document.getElementById("QRCode");
      if (qrElement) {
        new QRCode(qrElement, {
          text: this.promoteUrl,
          width: 100,
          height: 100,
          correctLevel: QRCode.CorrectLevel.H,
          colorDark: "#000000",
          colorLight: "#ffffff"
        });
      }
    });
  },
  activated() {

  },
  destroyed() {

  },
  methods: {
    // 复制邀请码
    copyInviteCode() {
      this.$Util.CopyText('IosCode', 'AppCode');
    },

    // 复制邀请链接
    copyInviteLink() {
      this.$Util.CopyText('IosLink', 'AppLink');
    },

    // 分享邀请
    shareInvite() {
      this.saveQRCode();
    },

    saveQRCode() {
      if(window.plus){
        this.saveN+=1
        var img = $('#QRCode').find('img').attr('src').replace('data:image/png;base64,','')
        var bm = new plus.nativeObj.Bitmap()
        bm.loadBase64Data(img, ()=>{
          bm.save( '_doc/promote'+this.saveN+'.jpg', {'overwrite':true,'format':'jpg'}, (e)=>{
            plus.gallery.save( e.target, (e)=>{
              this.$Dialog.Toast(this.$t('promote[7]'))
            },(err)=>{
              this.$Dialog.Toast(this.$t('promote[8]'))
            });
          }, (error)=>{
            this.$Dialog.Toast(this.$t('promote[8]'))
          });
          setTimeout(function(){
            bm.recycle();
          },1000);
        }, (err)=>{
          this.$Dialog.Toast(this.$t('promote[8]'))
        });
      }else{
        var img = $('#QRCode').find('img').attr('src')
        this.downCanvas(img)
      }
    },
    downCanvas(url) {
      var a = document.createElement('a')
      var event = new MouseEvent('click')
      a.download = 'promote'
      a.href = url
      a.dispatchEvent(event)
      if(!url){
        this.$Dialog.Toast(this.$t('promote[9]'))
      }
    },
  }
}
</script>
<style scoped>
.invite-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  position: relative;
}

/* 顶部导航栏 */
.header-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: transparent;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.back-button .van-icon {
  color: #ffffff;
  font-size: 18px;
}

.header-title {
  flex: 1;
  text-align: center;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-right: 40px; /* 平衡返回按钮的宽度 */
}

/* 主要内容区域 */
.main-content {
  padding-top: 60px;
  min-height: 100vh;
}

/* 英雄区域 */
.invite-hero {
  padding: 40px 24px 60px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: relative;
  z-index: 2;
  height: 260px; /* 为绝对定位的图片提供足够空间 */
}

.invite-title {
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
  margin: 0 0 30px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 2倍图处理 - 绝对定位背景 */
.hero-image {
  position: absolute;
  top: -125px;
  left: 50%;
  transform: translateX(-50%);
  width: 401px;
  /* height: 200px; */
  object-fit: contain;
  z-index: 1;
}

.character-left,
.character-right {
  position: relative;
  animation: bounce 2s infinite ease-in-out;
}

.character-right {
  animation-delay: -1s;
}

.character-head {
  width: 40px;
  height: 40px;
  background: #ffd700;
  border-radius: 50%;
  position: relative;
  margin-bottom: 5px;
}

.character-head::before {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #333;
  border-radius: 50%;
  top: 12px;
  left: 10px;
  box-shadow: 12px 0 0 #333;
}

.character-head::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 6px;
  background: #ff6b35;
  border-radius: 0 0 12px 12px;
  bottom: 8px;
  left: 14px;
}

.character-body {
  width: 30px;
  height: 40px;
  background: #4a90e2;
  border-radius: 15px;
  margin: 0 auto;
}

.gift-box {
  width: 30px;
  height: 30px;
  background: #ff6b35;
  border-radius: 4px;
  position: relative;
  animation: pulse 1.5s infinite;
}

.gift-box::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 4px;
  background: #ffd700;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.gift-box::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 100%;
  background: #ffd700;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 二维码和邀请码白色卡片 */
.qr-invite-card {
  background: #ffffff;
  margin: 0 24px 16px;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 内部左右布局 */
.qr-invite-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap;
}

/* 左侧二维码区域 */
.qr-code-area {
  flex: 1;
  flex-shrink: 0;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code {
  background: transparent;
  border: none;
  display: block;
  width: 100px;
  height: 100px;
}

.qr-code canvas {
  width: 100px !important;
  height: 100px !important;
  border-radius: 6px;
}

/* 右侧邀请码红色区域 */
.invite-code-area {
  flex: 1;
  min-width: 0;
  border-radius: 12px;
  padding: 12px 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 116px;
  position: relative;
  
}

.invite-code-area::before{
  content: '';
  display: block;
  width: 1px;
  height: 80%;
  background: #D9D9D9;
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.invite-code-label {
  font-size: 15px;
  background: linear-gradient(135deg, #FF0044 0%, #FF7179 50%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 6px;
  font-weight: 400;
}

.invite-code-value {
  font-size: 28px;
  font-weight: bold;
  background: linear-gradient(135deg, #FF0044 0%, #FF7179 50%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  line-height: 1.1;
}

.copy-code-button {
  background: linear-gradient(135deg, #FF0044 0%, #FF7179 80%);
  border-radius: 14px !important;
  padding: 8px 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #ffffff !important;
  height: 36px !important;
  width: auto !important;
}

/* 邀请链接白色卡片 */
.link-card {
  background: #ffffff;
  margin: 0 24px 16px;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.link-label {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  margin-bottom: 8px;
}

.link-input-container {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 0;
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.link-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 16px;
  font-size: 13px;
  color: #666666;
  outline: none;
  word-break: break-all;
}

.copy-icon {
  color: #ff4757;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 12px 16px;
  border-left: 1px solid #e0e0e0;
  background: #ffffff;
}

.copy-icon:hover {
  background: #f8f9fa;
  transform: scale(1.1);
}

/* 分享按钮 */
.share-button {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%) !important;
  border: none !important;
  border-radius: 24px !important;
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3) !important;
  transition: all 0.3s ease !important;
  color: #ffffff !important;
  width: calc(100% - 48px) !important;
  margin: 0 24px !important;
}

.share-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4) !important;
}

.share-button .van-icon {
  margin-right: 6px;
  font-size: 16px;
}

.share-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  filter: brightness(0) invert(1);
}



/* 隐藏元素 */
#IosCode,
#IosLink {
  /* position: absolute;
  opacity: 0; */
  pointer-events: none;
}

/* 添加一些装饰性元素 */
.invite-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="60" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  background-size: 200px 200px;
  animation: float 20s infinite linear;
  z-index: 1;
}
</style>