<template>
  <div class="PageBox">
    <div class="ScrollBox">
      <div class="CustomCell">
        <van-cell
          size="large"
          :icon="`./static/icon/info_001.png`"
          :title="$t('userInfo.default[1]')"
          center
          is-link
          @click="showHeader = true"
        >
          <img
            v-if="UserInfo && UserInfo.header"
            :src="`./static/head/${UserInfo.header}`"
            height="45"
            style="border-radius: 50%;"
          />
          <img
            v-else
            src="/static/head/head_1.png"
            height="45"
            style="border-radius: 50%;"
          />
        </van-cell>
        <van-cell
          size="large"
          :icon="`./static/icon/info_002.png`"
          :title="$t('userInfo.default[2]')"
          center
          :value="UserInfo.phone"
        />
        <van-cell
          size="large"
          :icon="`./static/icon/info_003.png`"
          :title="$t('usdt[0]')"
          center
          :value="$t('userInfo.default[8]')"
          to="bankCard"
          is-link
        />
        <!-- <van-cell
        size="large"
        :icon="`./static/icon/info_004.png`"
        :title="$t('userInfo.default[4]')"
        center
        :value="$t('userInfo.default[8]')"
        to="set/alipay"
        is-link
      /> -->
        <van-cell
          size="large"
          :icon="`./static/icon/info_005.png`"
          :title="$t('userInfo.default[5]')"
          center
          :value="$t('userInfo.default[8]')"
          to="set/info"
          is-link
        />
        <van-cell
          size="large"
          :icon="`./static/icon/info_006.png`"
          :title="$t('userInfo.default[6]')"
          center
          :value="$t('userInfo.default[8]')"
          is-link
          @click="showPassWord = true"
        />
        <van-cell
          size="large"
          :icon="`./static/icon/info_007.png`"
          :title="$t('userInfo.default[7]')"
          center
          :value="$t('userInfo.default[8]')"
          is-link
          @click="showPayWord = true"
        />
        <van-cell
          class="Cache"
          size="large"
          icon="delete"
          :title="$t('userInfo.default[13]')"
          center
          is-link
          @click="clearCache"
        />
      </div>
    </div>
    <van-action-sheet
      class="DrawPopup"
      v-model="showHeader"
      :title="$t('userInfo.default[9]')"
      close-on-popstate
    >
      <van-radio-group v-model="radioHeader">
        <van-grid clickable icon-size="45" column-num="5">
          <van-grid-item
            :icon="`./static/head/head_${item}.png`"
            v-for="item in 10"
            :key="item"
            @click="selectHeader(item)"
          />
        </van-grid>
      </van-radio-group>
    </van-action-sheet>
    <van-action-sheet
      class="DrawPopup"
      v-model="showPassWord"
      :title="$t('userInfo.default[10]')"
      close-on-popstate
    >
      <div class="DrawPopupBox">
        <van-field
          v-model.trim="postData.o_password"
          type="password"
          :label="$t('userInfo.label[0]')"
          :placeholder="$t('userInfo.placeholder[0]')"
          clearable
          size="large"
        />
        <van-field
          v-model.trim="postData.n_password"
          type="password"
          :label="$t('userInfo.label[1]')"
          :placeholder="$t('userInfo.placeholder[1]')"
          clearable
          size="large"
        />
        <van-field
          v-model.trim="postData.r_password"
          type="password"
          :label="$t('userInfo.label[2]')"
          :placeholder="$t('userInfo.placeholder[2]')"
          clearable
          size="large"
        />
      </div>
      <div style="padding: 16px;">
        <van-button
          type="danger"
          class="loginBtn"
          block
          style="font-size: 16px"
          @click="setUserInfo"
          >{{ $t("userInfo.default[12]") }}</van-button
        >
      </div>
    </van-action-sheet>
    <van-action-sheet
      class="DrawPopup"
      v-model="showPayWord"
      :title="$t('userInfo.default[11]')"
      close-on-popstate
    >
      <div class="DrawPopupBox">
        <van-field
          v-model.trim="postData.o_payword"
          type="password"
          :label="$t('userInfo.label[3]')"
          :placeholder="$t('userInfo.placeholder[3]')"
          clearable
          size="large"
          v-if="UserInfo.is_fund_password == 1"
        />
        <van-field
          v-model.trim="postData.n_payword"
          type="password"
          :label="$t('userInfo.label[4]')"
          :placeholder="$t('userInfo.placeholder[4]')"
          clearable
          size="large"
        />
        <van-field
          v-model.trim="postData.r_payword"
          type="password"
          :label="$t('userInfo.label[5]')"
          :placeholder="$t('userInfo.placeholder[5]')"
          clearable
          size="large"
        />
      </div>
      <div style="padding: 16px;">
        <van-button
          type="danger"
          block
          class="loginBtn"
          style="font-size: 16px"
          @click="setUserInfo"
          >{{ $t("userInfo.default[12]") }}</van-button
        >
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
export default {
  name: "Info",
  components: {},
  props: [],
  data() {
    return {
      showHeader: false,
      showPassWord: false,
      showPayWord: false,
      radioHeader: "",
      postData: {},
    };
  },
  computed: {},
  watch: {},
  created() {
    this.$parent.navBarTitle = this.$t("userInfo.default[0]");
    // 用户信息已在路由守卫或App.vue中获取，无需重复获取
    // this.$Model.GetUserInfo();
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    selectHeader(index) {
      this.postData.header = `head_${index}.png`;
      this.setUserInfo();
    },
    setUserInfo() {
      this.$Model.SetUserInfo(this.postData, (data) => {
        if (data.code == 1) {
          if (this.showHeader) {
            this.showHeader = false;
          }
          if (this.showPassWord) {
            this.showPassWord = false;
          }
          if (this.showPayWord) {
            this.showPayWord = false;
          }
          this.postData = {};
        }
      });
    },
    clearCache() {
      localStorage.clear();
      this.$Model.GetBackData();
      this.$router.push("/login");
    },
  },
};
</script>
<style scoped>
.PageBox >>> .van-grid-item__content {
  background: none !important;
}
.PageBox >>> .van-grid-item__content::after {
  border: none !important;
}
.PageBox {
  background-color: white !important;
}
.Site .van-cell {
  background: none !important;
}
.Site .van-cell::after {
  right: 0 !important;
}
.CustomCell {
  background-color: #f8f8f8;
  border-radius: 12px;
  width: calc(100% - 40px);
  margin: 0 auto;
  margin-top: 20px;
}
.van-cell__left-icon {
  width: 26px;
  height: 26px;
  margin-right: 10px;
}
.van-icon__image {
  width: 100%;
  height: 100%;
}
.van-cell__title {
  font-size: 14px;
}
.Cache .van-cell__left-icon {
  background-color: #444;
  border-radius: 100%;
  text-align: center;
}
</style>
