<template>
  <div class="Site PageBox">
    <van-nav-bar
      fixed
      :border="false"
      :title="$t('lineList[0]')"
      left-arrow
      @click-left="$router.go(-1)"
    />
    <div class="ScrollBox">
      <van-cell :title="$t('lineList[1]')" :value="currLine|ellipsisVal" />
      <van-radio-group v-model="lineIndex" @change="selectLine">
        <van-cell clickable v-for="(item,index) in listData" :key="index" @click="lineIndex=index">
          <template #title>
            {{$t('lineList[2]')}} {{item|ellipsisVal}}
            <i>{{timeList[index]}} ms</i>
          </template>
          <van-radio #right-icon :name="index" />
        </van-cell>
      </van-radio-group>
    </div>
  </div>
</template>
<script>
import Ping from 'web-pingjs'
export default {
  name: 'Lines',
  components: {

  },
  props: {},
  data() {
    return {
      lineIndex: 0,
      listData: '',
      timeList: [],
      currLine: localStorage['CurrLine']||ApiUrl,
      isStore: false,
    }
  },
  computed: {

  },
  filters: {
    ellipsisVal(val){
      return val.replace(/(\S*)\/\//,'').replace(/^(.{3}).*(.{4})$/, "$1***$2");
    }
  },
  watch: {
    'InitData.link'(data){
      if(!this.isStore){
        if(data.includes(ApiUrl)){
          this.listData = data
        }else{
          this.listData = [ApiUrl].concat(data)
        }
        this.lineIndex = this.listData.indexOf(this.currLine)
        this.pingLine(this.listData)
      }
    }
  },
  created() {
    if(this.InitData.link.length){
      this.isStore = true
      if(this.InitData.link.includes(ApiUrl)){
        this.listData = this.InitData.link
      }else{
        this.listData = [ApiUrl].concat(this.InitData.link)
      }
      this.lineIndex = this.listData.indexOf(this.currLine)
      this.pingLine(this.listData)
    }
  },
  mounted() {

  },
  activated() {

  },
  destroyed() {
    
  },
  methods: {
    pingLine(line) {
      this.timeList = [];
      line.forEach((host)=>{
        Ping(host).then((time)=> {
          this.timeList.push(time)
        }).catch((err)=> {
          this.timeList.push('-')
        })
      });
    },
    selectLine(val) {
      this.currLine = this.listData[val]
      localStorage['CurrLine'] = this.currLine
      this.UpdateApiUrl(this.currLine)
    },
  }
}
</script>
<style scoped>
.van-cell__value{
  flex: none;
}
.van-cell__title i{
  color: #1989fa;
  margin-left: 15px;
}
.van-radio{
  justify-content: flex-end;
}
</style>