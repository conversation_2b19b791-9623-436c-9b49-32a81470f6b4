# 全球支付功能实现文档

## 概述

本项目已成功集成全球支付功能，支持多个国家和地区的本地化支付方式，包括数字钱包、银行转账、扫码支付等多种支付渠道。

## 功能特性

### 🌍 支持的国家和地区
- 🇮🇩 印度尼西亚 (Indonesia) - IDR
- 🇮🇳 印度 (India) - INR  
- 🇹🇭 泰国 (Thailand) - THB
- 🇻🇳 越南 (Vietnam) - VND
- 🇲🇾 马来西亚 (Malaysia) - MYR
- 🇧🇷 巴西 (Brazil) - BRL

### 💳 支持的支付方式

#### 印度尼西亚 (ID)
- OVO钱包
- DANA钱包  
- GoPay钱包
- QRIS扫码支付
- 网银支付

#### 印度 (IN)
- Paytm
- PhonePe
- Google Pay
- UPI转账

#### 泰国 (TH)
- TrueMoney
- PromptPay
- 银行转账

#### 越南 (VN)
- MoMo
- ZaloPay
- 银行转账

#### 马来西亚 (MY)
- GrabPay
- Boost
- Touch 'n Go
- 网银支付

#### 巴西 (BR)
- PIX
- Boleto
- 信用卡

### 🌐 多语言支持

系统支持11种语言的完整本地化：
- 简体中文 (cn)
- 英语 (en)
- 印尼语 (id)
- 泰语 (th)
- 越南语 (vi)
- 印地语 (yd)
- 繁体中文 (ft)
- 西班牙语 (es)
- 日语 (ja)
- 马来语 (ma)
- 葡萄牙语 (pt)

## 技术实现

### 前端组件

#### 1. WatchPay支付选择页面 (`src/views/user/watchPay.vue`)
- 国家选择界面
- 支付方式选择
- 金额输入和验证
- 支付订单创建

#### 2. 支付订单详情页面 (`src/views/user/watchPayOrder.vue`)
- 订单状态显示
- 支付信息展示
- 二维码支付
- 订单状态自动刷新

### API接口

#### 1. 获取支持的国家列表
```javascript
this.$Model.GetWatchPayCountries((data) => {
  if (data.code === 1) {
    this.countries = data.data;
  }
});
```

#### 2. 获取支付方式
```javascript
this.$Model.GetWatchPayTypes({
  country_code: 'ID'
}, (data) => {
  if (data.code === 1) {
    this.paymentMethods = data.data;
  }
});
```

#### 3. 创建支付订单
```javascript
this.$Model.CreateWatchPayOrder({
  country_code: 'ID',
  pay_type: '202',
  amount: '1000',
  currency: 'IDR'
}, (data) => {
  if (data.code === 1) {
    // 处理支付成功
  }
});
```

#### 4. 查询订单状态
```javascript
this.$Model.GetWatchPayOrderStatus({
  order_number: 'WP123456789'
}, (data) => {
  if (data.code === 1) {
    this.orderInfo = data.data;
  }
});
```

### 路由配置

```javascript
// WatchPay支付选择页面
{
  path: "watchPay",
  name: "watchPay",
  component: () => import("@/views/user/watchPay.vue"),
  meta: {
    title: "全球支付",
    active: "user",
  },
}

// 支付订单详情页面
{
  path: "watchPayOrder/:orderNumber",
  name: "watchPayOrder",
  component: () => import("@/views/user/watchPayOrder.vue"),
  meta: {
    title: "支付订单",
    active: "user",
  },
  props: true,
}
```

### 多语言配置

每种语言都包含完整的全球支付相关翻译：

```javascript
// 中文示例 (src/i18n/cn.js)
globalPay: {
  title: "全球支付",
  selectCountry: "选择国家",
  selectPaymentMethod: "选择支付方式",
  // ... 更多翻译
}
```

## 开发和测试

### 模拟数据

项目包含完整的模拟数据系统 (`src/utils/globalPayMockData.js`)，在开发环境自动启用：

- 模拟国家列表
- 模拟支付方式
- 模拟订单创建
- 模拟订单状态查询

### 启动开发服务器

```bash
npm run dev
```

### 访问全球支付功能

1. 启动项目后访问充值页面
2. 在充值方式列表中选择"全球支付"
3. 选择国家和支付方式
4. 输入金额并创建订单

## 部署注意事项

### 后端API要求

需要实现以下API端点：

1. `POST /api/WatchPay/getCountries` - 获取支持的国家
2. `POST /api/WatchPay/getPayTypes` - 获取支付方式
3. `POST /api/WatchPay/createOrder` - 创建支付订单
4. `POST /api/WatchPay/getOrderStatus` - 查询订单状态

### 环境配置

- 开发环境：自动使用模拟数据
- 生产环境：连接真实的支付API

### 静态资源

确保以下资源文件已部署：
- 支付方式图标 (`src/static/images/payment/`)
- 全球支付图标 (`src/static/images/global-pay-icon.png`)

## 安全考虑

1. **数据验证**：前端和后端都需要验证支付金额和参数
2. **订单安全**：使用安全的订单号生成算法
3. **支付回调**：实现安全的支付状态回调机制
4. **用户认证**：确保只有登录用户可以创建支付订单

## 扩展性

系统设计具有良好的扩展性：

1. **新增国家**：在模拟数据中添加新国家配置
2. **新增支付方式**：在对应国家下添加新的支付方式
3. **新增语言**：在i18n文件中添加新语言的翻译
4. **自定义UI**：组件化设计便于自定义界面

## 总结

全球支付功能已完全集成到现有系统中，提供了：
- ✅ 完整的多国家支付支持
- ✅ 11种语言的本地化
- ✅ 响应式移动端界面
- ✅ 完整的开发和测试环境
- ✅ 良好的扩展性和维护性

系统现在可以为全球用户提供本地化的支付体验！
