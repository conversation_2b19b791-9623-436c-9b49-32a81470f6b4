<template>
  <div class="Main Site">
    <van-nav-bar
      fixed
      class="SiteNav"
      :border="false"
      :title="navBarTitle"
      :left-arrow="$route.name == 'user' || $route.name == 'teamReport' ? false : true"
      :right-text="rightText"
      @click-left="onClickLeft"
      @click-right="onClickRight"
      v-if="$route.name != 'user' && $route.name != 'myTask' && $route.name != 'teamReport' && $route.name != 'wheel' && $route.name != 'promote' && $route.name != 'watchPay'"
    />
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: "UserCenter",
  components: {},
  props: [],
  data() {
    return {
      rightText: "",
      navBarTitle: "",
    };
  },
  computed: {},
  watch: {
    $route(to, from) {
      switch (to.name) {
        case "recharge":
          this.rightText = this.$t("recharge.default[2]");
          break;
        case "newLc":
          this.rightText = this.$t("newLc[12]");
          break;
        case "postRecord":
          this.rightText = this.$t("postRecord[1]");
          break;
        default:
          this.rightText = "";
      }
    },
  },
  created() {
    switch (this.$route.name) {
      case "recharge":
        this.rightText = this.$t("recharge.default[2]");
        break;
      case "newLc":
        this.rightText = this.$t("newLc[12]");
        break;
      case "postRecord":
        this.rightText = this.$t("postRecord[1]");
        break;
      default:
        this.rightText = "";
    }
  },
  mounted() {
  },
  activated() {},
  destroyed() {},
  methods: {
    onClickLeft() {
      switch (this.$route.name) {
        case "taskRecord":
        case "postRecord":
        case "auditRecord":
        case "wallet":
          this.$router.push("/user");
          break;
        case "recharge":
          if (this.$children[1].showPrice) {
            this.$children[1].showPrice = false;
          } else {
            this.$router.go(-1);
          }
          break;
        default:
          this.$router.go(-1);
      }
    },
    onClickRight() {
      if (this.$route.name == "recharge") {
        this.$router.push("/user/wallet");
      }
      if (this.$route.name == "postRecord") {
        this.$router.push("/user/postTask");
      }
      if (this.$route.name == "newLc") {
        this.$router.push("/user/newLcList");
      }
    },
  },
};
</script>
<style scoped>
.SiteNav {
  background-color: white !important;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}
.SiteNav >>> .van-nav-bar__title {
  color: black;
  font-size: 1.1rem;
}
</style>
