<template>
  <div class="Site PageBox">
    <van-nav-bar
      fixed
      :border="false"
      left-arrow
      @click-left="$router.go(-1)"
      @click-right="$langList ? $router.push('/language') : false"
    >
      <template #right v-if="$langList">
        <svg style="color: #333; width: 18px; height: 18px;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <path d="M511.777943 68.191078c-245.204631 0-443.586864 198.60429-443.586864 443.808922s198.382233 443.808922 443.586864 443.808922 444.030979-198.60429 444.030979-443.808922S756.982574 68.191078 511.777943 68.191078zM819.11568 334.476841 688.191838 334.476841c-14.423501-55.476499-34.617425-108.733447-61.245899-157.995407C708.606797 204.440206 776.509303 261.025968 819.11568 334.476841zM512 158.506037c37.058011 53.256948 65.906036 112.505353 84.767616 175.96978L427.232384 334.475817C446.093964 271.01139 474.941989 211.762985 512 158.506037zM168.491459 600.76158c-7.322779-28.40391-11.538801-58.139142-11.538801-88.76158s4.216022-60.35767 11.538801-88.76158l149.785421 0c-3.550873 29.069059-5.991458 58.582233-5.991458 88.76158s2.440585 59.69252 6.213515 88.76158L168.491459 600.76158zM204.662263 689.523159l130.923842 0c14.423501 55.476499 34.617425 108.733447 61.245899 158.217465C315.171146 819.780829 247.267617 762.974032 204.662263 689.523159zM335.586105 334.476841 204.662263 334.476841c42.605354-73.449849 110.508883-130.257669 192.168718-158.217465C370.202507 225.743394 350.009605 279.000342 335.586105 334.476841zM512 865.493963c-36.835953-53.256948-65.683978-112.505353-84.767616-175.96978l169.535231 0C577.682955 752.987586 548.835953 812.235992 512 865.493963zM615.851253 600.76158 408.148747 600.76158c-4.216022-29.069059-7.100722-58.582233-7.100722-88.76158s2.8847-59.69252 7.100722-88.76158l207.702506 0c4.216022 29.069059 7.100722 58.582233 7.100722 88.76158S620.067274 571.69252 615.851253 600.76158zM627.167996 847.51959c26.628474-49.485041 46.821375-102.519931 61.245899-157.995407l130.923842 0C776.510326 762.974032 708.606797 819.558771 627.167996 847.51959zM705.500039 600.76158c3.550873-29.069059 6.213515-58.582233 6.213515-88.76158s-2.440585-59.69252-6.213515-88.76158l149.785421 0c7.322779 28.40391 11.760858 58.139142 11.760858 88.76158s-4.216022 60.35767-11.760858 88.76158L705.500039 600.76158z" fill="currentColor"/>
        </svg>
      </template>
    </van-nav-bar>

    <div class="Login ScrollBox">
      <div class="register-title">{{ InitData.setting && InitData.setting.web_title || '' }}</div>
      <div class="register-text">{{ $t("register.text[0]") }}</div>
      <van-form @submit="onSubmit">
        <div class="loginInputBox">
          <van-field left-icon="manager" clearable :border="false">
            <template #left-icon>
              <van-image
                width="24"
                height="24"
                style="margin-right: 8px;margin-top: 5px;"
                src="./static/icon/loginUserIcon.png"
              />
            </template>
            <template #input>
              <van-dropdown-menu>
                <van-dropdown-item
                  :title="`+${postData.dest}`"
                  ref="DropdownItem"
                >
                  <template>
                    <ul>
                      <li
                        v-for="(item, index) in areaList"
                        :key="index"
                        :class="{ on: postData.dest == item.id }"
                        @click="
                          (postData.dest = item.id), $refs.DropdownItem.toggle()
                        "
                      >
                        +{{ item.id }}&#12288;{{ item.name }}
                      </li>
                    </ul>
                  </template>
                </van-dropdown-item>
              </van-dropdown-menu>
              <input
                type="tel"
                v-model.trim="postData.username"
                :placeholder="$t('register.placeholder[0]')"
                style="border:0;flex:1;width:100px;padding-left: 50px;background: transparent"
              />
            </template>
          </van-field>
        </div>
        <div class="loginInputBox">
          <van-field
            left-icon="coupon"
            v-model.trim="postData.code"
            autocomplete="off"
            type="digit"
            :placeholder="$t('register.placeholder[6]')"
            clearable
            :border="false"
          >
            <template #left-icon>
              <van-image
                width="28"
                height="28"
                style="margin-right: 8px;margin-top: 10px;"
                src="./static/icon/codeIcon.png"
              />
            </template>
            <template #button>
              <van-image
                width="100"
                height="34"
                :src="codeImg"
                style="display: block;cursor: pointer;"
                @click="getCode"
              />
            </template>
          </van-field>
        </div>
        <div
          class="loginInputBox"
          v-if="InitData.setting && InitData.setting.is_sms == 1"
        >
          <van-field
            left-icon="coupon"
            v-model.trim="postData.smscode"
            autocomplete="off"
            type="digit"
            :placeholder="$t('register.placeholder[1]')"
            clearable
            :border="false"
          >
            <template #left-icon>
              <van-image
                width="28"
                height="28"
                style="margin-right: 8px;margin-top: 10px;"
                src="./static/icon/codeIcon.png"
              />
            </template>
            <template #button>
              <van-button
                size="mini"
                type="info"
                round
                native-type="button"
                @click="getSmsCode"
                :loading="isSendCode"
                :loading-text="$t('register.text[1]')"
                >{{ $t("register.text[2]") }}</van-button
              >
            </template>
          </van-field>
        </div>
        <div class="loginInputBox">
          <van-field
            type="password"
            v-model.trim="postData.password"
            :placeholder="$t('register.placeholder[2]')"
            clearable
            autocomplete="off"
            :border="false"
          >
            <template #left-icon>
              <van-image
                width="24"
                height="24"
                style="margin-right: 8px;margin-top: 5px;"
                src="./static/icon/loginPaswordIcon.png"
              />
            </template>
          </van-field>
        </div>
        <div class="loginInputBox">
          <van-field
            type="password"
            v-model.trim="postData.re_password"
            :placeholder="$t('register.placeholder[3]')"
            clearable
            autocomplete="off"
            :border="false"
          >
            <template #left-icon>
              <van-image
                width="24"
                height="24"
                style="margin-right: 8px;margin-top: 5px;"
                src="./static/icon/loginPaswordIcon.png"
              />
            </template>
          </van-field>
        </div>
        <div class="loginInputBox">
          <van-field
            left-icon="invition"
            :readonly="recommendDis"
            v-model.trim="postData.recommend"
            :placeholder="$t('register.placeholder[4]')"
            clearable
            autocomplete="off"
            :border="false"
          >
            <template #left-icon>
              <van-image
                width="24"
                height="24"
                style="margin-right: 8px;margin-top: 5px;"
                src="./static/icon/invitationCode.png"
              />
            </template>
          </van-field>
        </div>
        <van-cell :border="false">
          <van-checkbox
            slot="icon"
            v-model="isAgree"
            checked-color="#FF0F23"
            icon-size="16"
            style="margin-left: 22px;"
          />
          <a
            href="javascript:;"
            @click="openArticle"
            style="margin-left: 8px;font-size: 12px;color: #999;text-decoration: underline"
            >{{ $t("register.text[6]") }}</a
          >
        </van-cell>

        <div style="padding: 20px 16px">
          <van-button
            size="large"
            block
            type="danger"
            round
            class="loginBtn"
            :loading="isSubmit"
            :loading-text="$t('register.text[3]')"
            style="font-size: 18px;"
            >{{ $t("register.text[4]") }}
          </van-button>
          <div
            slot="a"
            style="font-size: 12px;margin-top: 10px;color: #FF0F23;"
            @click="appDown"
            v-if="!isPlus"
          >
            {{ $t("register.text[5]") }}
          </div>
          <i18n
            path="register.i18n[0]"
            tag="div"
            style="text-align: left;color: #fff;margin-top: 30px"
            v-if="isPlus"
          >
            <router-link class="href" slot="a" to="/login">{{
              $t("register.i18n[1]")
            }}</router-link>
            <router-link class="href fr" slot="line" to="/line">{{
              $t("line")
            }}</router-link>
          </i18n>
        </div>
      </van-form>
    </div>
    <div class="BrowserTips" v-show="showBrowserTips">
      <img :src="'./static/images/down-zy.png'" width="80%" />
    </div>
  </div>
</template>

<script>
export default {
  name: "Register",
  components: {},
  props: ["recommendId"],
  data() {
    return {
      postData: {
        dest: '62',
        username: "",
        password: "",
        re_password: "",
        smscode: "",
        code: "",
        code_rand: "",
        recommend: this.recommendId || "",
      },
      recommendDis: this.recommendId ? true : false,
      areaList: areaList,
      isSendCode: false,
      isSubmit: false,
      codeImg: "",
      showBrowserTips: false,
      downUrl: "",
      isPlus: false,
      isAgree: true,
    };
  },
  computed: {},
  watch: {},
  created() {
    const lang = localStorage["Language"] || Language;
    this.downUrl = this.InitData.setting.app_down + lang.toUpperCase();
    this.$Model.SmsCode((data) => {
      this.areaList = data;
      // 优先选择62区号，如果不存在则选择第一个
      const area62 = data.find(item => item.id == '62');
      this.postData.dest = area62 ? '62' : data[0].id;
    });
    this.getCode();
    this.checkUserAgent();
  },
  mounted() {
    if (window.plus) {
      this.isPlus = true;
    }
  },
  activated() {},
  destroyed() {},
  methods: {
    appDown() {
      const lang = localStorage["Language"] || Language;
      const downUrl = this.InitData.setting.app_down + lang.toUpperCase();
      this.$Util.OpenUrl(downUrl);
    },
    openArticle() {
      this.$router.push(
        `/article/terms/${
          this.InitData.disclaimerList.length
            ? this.InitData.disclaimerList[0].id
            : ""
        }`
      );
    },
    getCode() {
      this.postData.code_rand = new Date().getTime();
      this.codeImg =
        this.ApiUrl + "/api/Account/code?code_rand=" + this.postData.code_rand;
    },
    onSubmit() {
      if (!this.postData.username) {
        this.$Dialog.Toast(this.$t("register.placeholder[0]"));
        return;
      }
      // if(!this.postData.recommend){
      //   this.$Dialog.Toast(this.$t('register.placeholder[4]'))
      //   return
      // }
      if (!this.postData.smscode && this.InitData.setting.is_sms == 1) {
        this.$Dialog.Toast(this.$t("register.placeholder[1]"));
        return;
      }
      if (!this.postData.code && this.InitData.setting.is_sms == 2) {
        this.$Dialog.Toast(this.$t("register.placeholder[6]"));
        return;
      }
      if (!this.postData.password) {
        this.$Dialog.Toast(this.$t("register.placeholder[2]"));
        return;
      }
      if (!this.postData.re_password) {
        this.$Dialog.Toast(this.$t("register.placeholder[3]"));
        return;
      }
      if (this.postData.password != this.postData.re_password) {
        this.$Dialog.Toast(this.$t("register.placeholder[5]"));
        return;
      }
      if (!this.isAgree) {
        this.$Dialog.Toast(this.$t("register.placeholder[8]"));
        return;
      }
      this.isSubmit = true;
      this.$Model.UserRegister(this.postData, (data) => {
        this.isSubmit = false;
        this.getCode();
      });
    },
    getSmsCode() {
      if (!this.postData.username) {
        this.$Dialog.Toast(this.$t("register.placeholder[0]"));
        return;
      }
      // if(!this.postData.recommend){
      //   this.$Dialog.Toast(this.$t('register.placeholder[4]'))
      //   return
      // }
      if (!this.postData.code) {
        this.$Dialog.Toast(this.$t("register.placeholder[6]"));
        return;
      }
      this.isSendCode = true;
      this.$Model.GetSMSCode(
        {
          phone: this.postData.username,
          dest: this.postData.dest,
          code: this.postData.code,
          recommend: this.postData.recommend,
          code_rand: this.postData.code_rand,
        },
        (data) => {
          this.isSendCode = false;
        }
      );
    },
    checkUserAgent(callback) {
      var ua = navigator.userAgent;
      var is_WeiXin = () => {
        if (/(MicroMessenger)/.test(ua)) {
          return true;
        }
        return false;
      };
      var is_QQ = () => {
        if (/(QQ)/.test(ua)) {
          return true;
        }
        return false;
      };
      var is_Android = () => {
        if (/(Android)/.test(ua)) {
          return true;
        }
        return false;
      };
      var is_iOS = () => {
        if (/(iPod|iPhone|iPad)/.test(ua)) {
          return true;
        }
        return false;
      };
      if (is_WeiXin()) {
        this.showBrowserTips = true;
      }
      if (is_iOS()) {
        callback && callback("IOS");
      } else {
        callback && callback("Android");
      }
    },
  },
};
</script>

<style scoped>
.PageBox {
  background: white;
}
.van-dropdown-menu {
  position: absolute;
  left: 0;
}
.van-dropdown-menu >>> .van-dropdown-menu__bar {
  height: auto;
  box-shadow: none;
  background: transparent;
}
.van-dropdown-menu >>> .van-dropdown-menu__title {
  padding: 0 10px 0 0;
  margin-right: 10px;
  color: #888;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul {
  padding: 0 15px;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul li {
  padding: 8px 0;
  width: 100%;
  font-size: 14px;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul li.on {
  color: #1989fa;
}
.van-dropdown-menu >>> .van-dropdown-item__content ul li + li {
  border-top: 1px #f5f5f5 solid;
  color: #333;
}
.BrowserTips {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: right;
  z-index: 99999;
}
.van-nav-bar >>> .van-nav-bar__text {
  color: #999;
}
.register-title {
  font-size: 46px;
  font-weight: 600;
  color: #292929;
  margin-top: 20px;
}
.register-text {
  font-size: 24px;
  color: #292929;
  text-align: left;
  font-weight: 600;
  margin-top: 10px;
  text-indent: 20px;
}
.loginInputBox {
  width: calc(100% - 40px);
  height: 60px;
  border-radius: 12px;
  background: #f8f8f8;
  margin: 0 auto;
  margin-top: 20px;
  display: flex;
  align-items: center;
}
.loginInputBox >>> .van-dropdown-menu__title::after {
  border: 5px solid;
  border-color: transparent transparent #5a5a68 #5a5a68;
  margin-top: -8px;
  right: -7px;
}
.loginInputBox >>> input {
  font-size: 16px;
  color: #7d7d7d;
}
.loginInputBox >>> .van-field {
  display: flex;
  align-items: center;
}
</style>
