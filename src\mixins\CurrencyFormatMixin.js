// 货币格式化 Mixin
export default {
  methods: {
    // 格式化余额显示
    formatBalanceDisplay(value) {
      return this.$Util.FormatCurrency(value, this.$i18n.locale);
    },

    // 格式化余额工具提示（显示完整数值）
    formatBalanceTooltip(value) {
      return this.$Util.FormatCurrencyTooltip(value, this.$i18n.locale);
    },

    // 格式化货币（带自定义选项）
    formatCurrency(value, options = {}) {
      return this.$Util.FormatCurrency(value, this.$i18n.locale, options);
    },

    // 格式化大数值（总是显示完整数值，不使用K/M缩写）
    formatFullCurrency(value) {
      const num = Number(value) || 0;
      return num.toLocaleString(this.$i18n.locale, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
  }
}
