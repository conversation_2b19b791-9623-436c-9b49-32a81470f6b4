# 全球支付功能使用指南

## 🚀 快速开始

### 1. 启动项目
```bash
npm install
npm run dev
```

### 2. 访问全球支付功能
1. 打开浏览器访问 `http://localhost:8080`
2. 登录或注册账户
3. 进入"我的钱包" -> "充值"
4. 选择"全球支付"选项

## 📱 用户操作流程

### 步骤1：选择国家
- 在全球支付页面选择您所在的国家
- 系统支持6个主要国家/地区：
  - 🇮🇩 印度尼西亚
  - 🇮🇳 印度
  - 🇹🇭 泰国
  - 🇻🇳 越南
  - 🇲🇾 马来西亚
  - 🇧🇷 巴西

### 步骤2：选择支付方式
- 根据选择的国家，显示相应的本地支付方式
- 每种支付方式都有详细的说明和图标

### 步骤3：输入金额
- 输入要充值的金额
- 系统会自动验证最小和最大金额限制
- 显示当地货币单位

### 步骤4：创建订单
- 点击"立即支付"按钮
- 系统创建支付订单
- 跳转到支付页面或显示支付信息

### 步骤5：完成支付
- 根据支付方式完成支付
- 查看订单状态
- 等待资金到账

## 🌐 多语言支持

系统根据用户的语言设置自动显示相应语言：

### 支持的语言
- **简体中文** (cn) - 中国大陆用户
- **English** (en) - 国际用户
- **Bahasa Indonesia** (id) - 印尼用户
- **ไทย** (th) - 泰国用户
- **Tiếng Việt** (vi) - 越南用户
- **हिन्दी** (yd) - 印度用户
- **繁體中文** (ft) - 台湾、香港用户
- **Español** (es) - 西班牙语用户
- **Bahasa Melayu** (ma) - 马来西亚用户
- **Português** (pt) - 巴西、葡萄牙用户
- **日本語** (ja) - 日本用户

### 语言切换
- 在语言设置页面选择语言
- 全球支付界面会自动更新为选择的语言
- 国家名称、支付方式、提示信息都会本地化

## 💳 支付方式详解

### 印度尼西亚 (IDR)
- **OVO钱包** - 最受欢迎的数字钱包
- **DANA钱包** - 广泛使用的移动支付
- **GoPay** - Gojek生态系统钱包
- **QRIS扫码** - 统一二维码支付标准
- **网银支付** - 传统银行转账

### 印度 (INR)
- **Paytm** - 领先的数字钱包
- **PhonePe** - UPI支付平台
- **Google Pay** - 谷歌支付服务
- **UPI转账** - 统一支付接口

### 泰国 (THB)
- **TrueMoney** - 主要数字钱包
- **PromptPay** - 国家级即时支付系统
- **银行转账** - 直接银行转账

### 越南 (VND)
- **MoMo** - 领先的移动钱包
- **ZaloPay** - Zalo生态系统支付
- **银行转账** - 传统银行转账

### 马来西亚 (MYR)
- **GrabPay** - Grab生态系统钱包
- **Boost** - 流行的数字钱包
- **Touch 'n Go** - 电子钱包和卡片
- **网银支付** - 在线银行转账

### 巴西 (BRL)
- **PIX** - 巴西即时支付系统
- **Boleto** - 传统银行票据支付
- **信用卡** - 信用卡支付

## 🔧 开发者信息

### 模拟数据
在开发环境中，系统使用模拟数据：
- 模拟国家列表和支付方式
- 模拟订单创建和状态查询
- 无需真实的支付网关连接

### API端点
生产环境需要实现以下API：
```
POST /api/WatchPay/getCountries
POST /api/WatchPay/getPayTypes
POST /api/WatchPay/createOrder
POST /api/WatchPay/getOrderStatus
```

### 自定义配置
可以通过修改以下文件来自定义：
- `src/utils/globalPayMockData.js` - 模拟数据
- `src/i18n/*.js` - 多语言翻译
- `src/views/user/watchPay.vue` - 主界面
- `src/views/user/watchPayOrder.vue` - 订单页面

## 🎨 界面特性

### 响应式设计
- 适配移动端和桌面端
- 使用Vant UI组件库
- 现代化的卡片式布局

### 用户体验
- 直观的国家选择网格
- 清晰的支付方式列表
- 实时的金额验证
- 友好的错误提示

### 视觉设计
- 国家旗帜图标
- 支付方式图标
- 渐变色背景
- 圆角卡片设计

## 🔒 安全特性

### 数据验证
- 前端输入验证
- 金额范围检查
- 支付方式验证

### 订单安全
- 唯一订单号生成
- 订单状态跟踪
- 支付超时处理

### 用户认证
- 登录状态验证
- Token认证机制
- 安全的API调用

## 📊 监控和分析

### 订单跟踪
- 实时订单状态更新
- 自动刷新机制
- 支付进度显示

### 错误处理
- 网络错误重试
- 友好的错误消息
- 详细的错误日志

### 性能优化
- 懒加载组件
- 图片优化
- API响应缓存

## 🆘 常见问题

### Q: 如何添加新的国家？
A: 在`globalPayMockData.js`中添加新的国家配置，并在多语言文件中添加翻译。

### Q: 如何添加新的支付方式？
A: 在对应国家的支付方式列表中添加新的配置项。

### Q: 如何自定义界面样式？
A: 修改Vue组件中的CSS样式，或者在全局样式文件中添加自定义样式。

### Q: 如何集成真实的支付网关？
A: 替换Model.js中的模拟API调用为真实的后端API调用。

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 检查网络连接状态
3. 确认API端点配置正确
4. 联系技术支持团队

---

**全球支付功能现已完全集成，为全球用户提供便捷的本地化支付体验！** 🌍💳✨
