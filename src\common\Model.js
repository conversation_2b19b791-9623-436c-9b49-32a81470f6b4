import store from '@/store'
import router from '@/router'
import axios from './Axios'
import $Dialog from './Dialog'
import i18n, {
  SetLanguage
} from '@/i18n'

const model = {
  GetBackData(callback) {
    const getData = ({
      data
    }) => {
      store.dispatch('UpdateInitData', data.info || '');
      localStorage['UploadApi'] = data.info.setting.up_url;
      callback && callback(data.info);
    }
    axios.post('Common/BackData', '', {
      noLogin: true
    }).then(getData);
  },
  GetLanguage(callback) {
    const getData = ({
      data
    }) => {
      // 获取用户本地设置和服务器默认语言
      const userLanguage = localStorage['Language'];
      const serverLanguage = data.Language.default_language;

      // 如果用户没有手动设置过语言，优先使用服务器设置，否则使用本地设置
      const is_self_change = localStorage["is_self_change"] || 0;
      const finalLanguage = is_self_change ? userLanguage : (serverLanguage || userLanguage || 'cn');

      // 将简化语言代码转换为完整语言代码
      let fullLanguageCode = 'zh-CN'; // 默认中文
      switch(finalLanguage) {
        case 'cn':
          fullLanguageCode = 'zh-CN';
          break;
        case 'ft':
          fullLanguageCode = 'zh-TW';
          break;
        case 'en':
          fullLanguageCode = 'en-US';
          break;
        case 'id':
          fullLanguageCode = 'id-ID';
          break;
        case 'th':
          fullLanguageCode = 'th-TH';
          break;
        case 'vi':
          fullLanguageCode = 'vi-VN';
          break;
        case 'yd':
          fullLanguageCode = 'yd-YD';
          break;
        case 'es':
          fullLanguageCode = 'es-ES';
          break;
        case 'ja':
          fullLanguageCode = 'ja-JP';
          break;
        case 'ma':
          fullLanguageCode = 'ma-MA';
          break;
        case 'pt':
          fullLanguageCode = 'pt-PT';
          break;
        default:
          fullLanguageCode = serverLanguage || 'zh-CN';
      }

      SetLanguage(fullLanguageCode);
      callback && callback(data.Language);
    };
    axios.post('Common/GetLanguage', '', {
      noLogin: true
    }).then(getData)
  },
  Login(json, callback) {
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        // 使用统一的登录信息设置
        localStorage['MiName'] = data.info.username;
        localStorage['Token'] = data.info.token;
        localStorage['UserId'] = data.info.userid;
        localStorage['Uid'] = data.info.uid;

        // 更新用户信息到全局状态（仅保存在内存中）
        store.dispatch('UpdateUserInfo', data.info);

        console.log('登录成功，用户信息已保存到全局状态（仅内存）');

        // 页面跳转逻辑
        if (localStorage['FromPage']) {
          router.replace(localStorage['FromPage']);
          localStorage.removeItem('FromPage');
        } else {
          router.replace('/');
        }
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }

    const handleError = (error) => {
      console.error('登录请求失败:', error);
      callback && callback({ code: 0, code_dec: '网络请求失败' });
      $Dialog.Toast('网络请求失败');
    }

    axios.post('User/Login', json, {
      noLogin: true
    }).then(getData).catch(handleError);
  },
  Logout(callback) {
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        localStorage.removeItem('Token');
        localStorage.removeItem('UserInfo'); // 清理可能残留的localStorage数据
        localStorage.removeItem('BankCardList');
        // 清除内存中的用户信息
        store.dispatch('UpdateUserInfo', '');
        store.dispatch('UpdateBankCardList', []);
        this.GetBackData();
        router.replace('/');
      }
      $Dialog.Toast(data.code_dec);
    }
    axios.post('User/Logout').then(getData);
  },
  UserRegister(json, callback) {
    $Dialog.Loading(i18n.t('dialog[5]'));
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        model.Login({
          username: json.username,
          password: json.password
        })
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('user/Register', json, {
      noLogin: true
    }).then(getData);
  },
  SmsCode(callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('sms/smsCode', '', {
      noLogin: true
    }).then(getData);
  },
  GetSMSCode(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('sms/sendSMSCode', json, {
      noLogin: true
    }).then(getData);
  },
  GetEmailCode(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('email/sendCode', json, {
      noLogin: true
    }).then(getData);
  },
  GetUserInfo(callback) {
    console.log('=== 开始获取用户信息 ===');
    console.log('当前Token:', localStorage['Token']);

    const getData = ({
      data
    }) => {
      console.log('GetUserInfo API响应:', data);

      if (data.code == 1) {
        console.log('用户信息获取成功，开始更新状态...');

        // 更新Vuex store中的用户信息（仅保存在内存中）
        store.dispatch('UpdateUserInfo', data.info);

        // 用户信息不进行持久化保存
        // localStorage['UserInfo'] = JSON.stringify(data.info);

        console.log('用户信息已更新到全局状态:', data.info);
        console.log('Vuex状态已更新:', store.state.UserInfo);
      } else {
        // 如果获取用户信息失败，清除相关数据
        console.error('获取用户信息失败:', data.code_dec);
        if (data.code > 200 && data.code < 206) {
          // Token失效的情况已经在axios拦截器中处理
          console.log('Token失效，由拦截器处理');
          return;
        }
      }
      callback && callback(data);
    }

    // 添加错误处理
    const handleError = (error) => {
      console.error('获取用户信息请求失败:', error);
      callback && callback({ code: 0, code_dec: '网络请求失败' });
    }

    console.log('发送获取用户信息请求...');
    axios.post('user/getUserInfo').then(getData).catch(handleError);
  },
  SetUserInfo(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        this.GetUserInfo();
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('user/setuserinfo', json).then(getData);
  },
  CreateOrder(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        router.push('/user/invest');
        this.GetUserInfo();
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('Order/createOrder', json).then(getData);
  },
  OrderList(callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Order/Orderlist').then(getData);
  },
  OrderRecordList(id, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Order/orderRecordList', {
      orderid: id
    }).then(getData);
  },
  FundDetails(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Transaction/FundDetails', json).then(getData);
  },
  GetDrawRecord(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Transaction/getDrawRecord', json).then(getData);
  },
  Draw(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        this.GetUserInfo();
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('Transaction/draw', json).then(getData);
  },
  Transfer(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        this.GetUserInfo();
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('Transaction/Transfer', json).then(getData);
  },
  AddBankCard(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      if (data.code == 1) {
        this.GetBankCardList()
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('Account/AddBankCard', json).then(getData);
  },
  GetBankCardList(callback) {
    const getData = ({
      data
    }) => {
      store.dispatch('UpdateBankCardList', data.data || []);
      callback && callback(data);
    }
    axios.post('Account/getBankCardList').then(getData);
  },
  GetRechargeRecord(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Transaction/getRechargeRecord', json).then(getData);
  },
  GetRechargeType(callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Transaction/getRechargetype', {
      type: 'app'
    }).then(getData);
  },
  RechargeOrder(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      if (data.url) {
        router.push('/user/wallet');
      } else {
        router.push('/user/recharge/' + data.orderNumber);
      }
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('Recharge/newRechargeOrder', json).then(getData);
  },
  GetRechargeInfo(orderId, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Recharge/getRechargeInfo', {
      orderNumber: orderId
    }).then(getData);
  },
  SetOrderInfo(json, callback) {
    console.log(json)
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getData = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('Recharge/setOrderInfo', json).then(getData);
  },
  UploadImg(json, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('User/UploadImg', json, {
      fromData: true,
      diyApi: true
    }).then(getResponse);
  },
  BuyVip(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('User/userBuyVip', json).then(getResponse);
  },
  PostTask(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('task/publishTask', json).then(getResponse);
  },
  GetTaskList(json, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('task/getTaskList', json).then(getResponse);
  },
  ReceiveTask(id, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    const handleError = (error) => {
      console.error('接收任务失败:', error);
      if (error.response && error.response.data &&
          typeof error.response.data === 'string' &&
          error.response.data.includes('trial time')) {
        $Dialog.Toast('系统维护中，请稍后再试');
      }
      // 调用回调函数，传递错误状态
      callback && callback({
        code: 0,
        code_dec: '接收任务失败，请稍后重试'
      });
    }
    axios.post('task/receiveTask', {
      id: id
    }).then(getResponse).catch(handleError);
  },
  GetTaskinfo(id, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('task/getTaskinfo', {
      id: id
    }).then(getResponse);
  },
  GetTaskRecord(json, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('task/taskOrderlist', json).then(getResponse);
  },
  SubmitTask(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('task/taskOrderSubmit', json).then(getResponse);
  },
  CancelTask(id, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('task/revokeTask', {
      id: id
    }).then(getResponse);
  },
  TaskOrderInfo(id, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('task/taskorderinfo', {
      order_id: id
    }).then(getResponse);
  },
  AuditTask(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    }
    axios.post('task/taskOrderTrial', json).then(getResponse);
  },
  DailyReport(callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('User/dailyReport').then(getResponse);
  },
  CreditList(callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('user/getUserCreditList').then(getResponse);
  },
  TeamReport(json, callback) {
    $Dialog.Loading(i18n.t('dialog[6]'));
    const getResponse = ({
      data
    }) => {
      $Dialog.Close();
      callback && callback(data);
    }
    axios.post('user/teamReport', json).then(getResponse);
  },
  GetStatisticsInfo(callback) {
    const getResponse = ({
      data
    }) => {
      if (data.code == 1) {
        store.dispatch('UpdateStatisticalData', data.info);
      }
      callback && callback(data);
    }
    axios.post('user/getStatisticsInfo').then(getResponse);
  },
  GetPayBank(json, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('Account/GetPayBankCode', json).then(getResponse);
  },

  // 统一支付相关API
  GetPaymentTypes(json, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    // 添加语言参数
    const lang = localStorage['Language'] || 'id'; // 默认使用印尼语
    const params = { ...json, lang };
    axios.post('Transaction/getPayTypes', params).then(getResponse);
  },

  CreateUnifiedOrder(json, callback) {
    $Dialog.Loading(i18n.t('dialog[2]'));

    const getResponse = ({
      data
    }) => {
      $Dialog.Close();
      callback && callback(data);
    }
    axios.post('Transaction/createUnifiedOrder', json).then(getResponse);
  },

  GetPaymentOrderStatus(json, callback) {
    const getResponse = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post('WatchPay/getOrderStatus', json).then(getResponse);
  },
  newLc(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    };
    axios.post("Yuebao/getYuebaoList", json).then(getData)
  },
  newLcTj(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    };
    axios.post("Yuebao/payYuebao", json).then(getData)
  },
  newList(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    };
    axios.post("Yuebaojilu/getYuebaojiluList", json).then(getData)
  },
  yeb(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    };
    axios.post("Yuebao/showMoney", json).then(getData)
  },
  signin(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
      $Dialog.Toast(data.code_dec);
    };
    axios.post("user/signin", json).then(getData)
  },
  addWheelReward(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    };
    axios.post("user/addWheelReward", json).then(getData)
  },
  getWheelWiner(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post("user/getWheelWiner",json).then(getData)
  },
  // 获取奖品配置
  getWheelPrizes(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post("wheel/getPrizes", json).then(getData)
  },
  // 获取剩余抽奖次数
  getWheelRemainingTimes(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post("wheel/getRemainingTimes", json).then(getData)
  },
  // 获取中奖记录
  getWheelWinRecords(json, callback) {
    const getData = ({
      data
    }) => {
      callback && callback(data);
    }
    axios.post("wheel/getWinRecords", json).then(getData)
  },
}

export default model
