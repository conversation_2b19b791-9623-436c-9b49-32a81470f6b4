<template>
  <div class="Site PageBox">
    <van-nav-bar
      fixed
      :border="false"
      :title="$t('Activity[0]')"
      left-arrow
      @click-left="$router.go(-1)"
    />
    <van-loading class="DataLoad" size="60px" vertical v-if="isLoad">{{$t('Activity[1]')}}</van-loading>
    <iframe id="iframe" src="" width="100%" height="100%" frameborder="0" style="position: relative;z-index: 99"></iframe>
  </div>
</template>
<script>
export default {
  name: 'Service',
  components: {

  },
  props: {},
  data() {
    return {
      isLoad: true
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    
  },
  mounted() {
    const iframe = document.getElementById("iframe")
    iframe.src = `${this.$store.state.InitData.setting.activity_url}?id=${this.$store.state.UserInfo.userid||''}`
    if (iframe.attachEvent){ 
      iframe.attachEvent("onload", ()=>{ 
        this.isLoad = false
      }); 
    } else {
      iframe.onload = ()=>{
        this.isLoad = false
      };
    }
  },
  activated() {

  },
  destroyed() {
    
  },
  methods: {
 
  }
}
</script>
<style scoped>
.van-nav-bar>>>.van-nav-bar__title{
  max-width: none;
}
</style>