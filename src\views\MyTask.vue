<template>
  <div class="Site IndexBox">
    <van-nav-bar
      fixed
      :border="false"
      :left-text="webTitle"
      left-arrow
      @click-left="$router.go(-1)"
    >
    </van-nav-bar>
    <!-- 固定的Tab栏 -->
    <div class="fixed-tabs-container">
      <van-tabs
        :ellipsis="false"
        :border="false"
        color="#4087f1"
        title-active-color="#fff"
        title-inactive-color="#292929"
        line-width="0"
        v-model="tabsIndex"
        @change="changeTabs"
      >
        <van-tab v-for="item in taskTabs" :title="item.text" :key="item.state">
        </van-tab>
      </van-tabs>
    </div>
    <!-- 可滚动的内容区域 -->
    <div class="scrollable-content">
      <div class="tab-content" v-show="tabsIndex === index" v-for="(item, index) in taskTabs" :key="item.state">
        <van-pull-refresh
          class="customPullRefresh"
          v-model="isRefresh"
          @refresh="onRefresh"
        >
          <van-list
            v-model="isLoad"
            :finished="isFinished"
            :finished-text="
              listData[index].length ? $t('vanPull[0]') : $t('vanPull[1]')
            "
            @load="onLoad"
            :class="{ Empty: !listData[index].length }"
          >
            <van-cell
              class="TaskItem"
              title-class="record"
              :border="false"
              v-for="(taskItem, taskIndex) in listData[index]"
              :key="taskItem.order_id"
              @click="onClickCell(taskItem.order_id, $event)"
            >
              <!-- <div class="icon" slot="icon">
                <a href="javascript:;" v-if="taskItem.is_fx == 1">
                  <img :src="InitData.setting.up_url + taskItem.icon" />
                </a>
                <a
                  href="javascript:;"
                  @click.stop="$Util.OpenUrl(taskItem.link_info)"
                  v-else
                >
                  <img :src="InitData.setting.up_url + taskItem.icon" />
                </a>
              </div> -->
              <template #title>
                <h4>
                  {{
                    taskItem.title ||
                      taskItem.content ||
                      taskItem.group_name + $t("task.default[1]")
                  }}
                </h4>
                <!-- <h4 style="margin-top: 4px">
                  {{ taskItem.group_info }}
                </h4> -->
                <!-- 价格和佣金信息 -->
                <div class="task-financial-info">
                  <p class="price-info" v-if="taskItem.purchase_price || taskItem.reward_price">
                    <span class="price-label">{{ $t("task.show[22]") }}:</span>
                    <span class="price-value">{{ InitData.currency }}{{ Number(taskItem.purchase_price || taskItem.reward_price || 0).toFixed(2) }}</span>
                  </p>
                  <p class="commission-info" v-if="taskItem.task_commission">
                    <span class="commission-label">{{ $t("task.show[21]") }}:</span>
                    <span class="commission-value">+{{ InitData.currency }}{{ Number(taskItem.task_commission).toFixed(2) }}</span>
                  </p>
                </div>
                <!-- <p style="color: #fff" v-if="taskItem.requirement">
                  ({{ taskItem.requirement }})
                </p> -->
                <p>{{ $t("task.default[2]") }}：{{ taskItem.add_time }}</p>
                <p>
                  {{ $t("task.default[3]") }}：{{
                    tabsState == 1 || tabsState == 5
                      ? taskItem.add_time
                      : taskItem.handle_time
                  }}
                </p>
                <p class="href" v-if="taskItem.is_fx != 1">
                  <a :href="taskItem.link_info" target="_blank" @click.stop="">{{
                    $t("task.default[4]")
                  }}</a>
                  <a
                    href="javascript:;"
                    @click.stop="
                      $Util.CopyText(`IosLink${taskIndex}`, `AppLink${taskIndex}`)
                    "
                    >{{ $t("task.default[5]") }}</a
                  >
                </p>
                <span
                  :id="`IosLink${taskIndex}`"
                  style="position: absolute;opacity: 0"
                  >{{ taskItem.link_info }}</span
                >
                <input
                  :id="`AppLink${taskIndex}`"
                  type="text"
                  :value="taskItem.link_info"
                  style="position: absolute;opacity: 0"
                />
              </template>
              <!-- 进行中状态：显示上传和提交按钮 -->
              <div class="state" v-if="tabsState == 1">
                <van-uploader
                  upload-icon="photo"
                  v-model="fileList[taskIndex]"
                  :after-read="afterRead"
                  multiple
                />
                <van-button
                  type="info"
                  size="mini"
                  round
                  class="submitBtn"
                  @click.stop="submitTask(taskItem.order_id, taskIndex)"
                  >{{ $t("task.default[7]") }}</van-button
                >
              </div>
              <!-- 审核中状态：显示状态图标 -->
              <div class="state" v-else-if="tabsState == 2">
                <div class="completed-status">
                  <img
                    :src="`./static/icon/state${taskItem.status || 2}-${$i18n.locale}.png`"
                    height="50"
                  />
                </div>
              </div>
              <!-- 已完成状态：显示状态图标 -->
              <div class="state" v-else>
                <div class="completed-status">
                  <img
                    :src="`./static/icon/state${taskItem.status || 3}-${$i18n.locale}.png`"
                    height="50"
                  />
                </div>
              </div>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
export default {
  name: "TaskRecord",
  components: {},
  props: ["taskState"],
  data() {
    return {
      listData: [],
      isLoad: false,
      isFinished: false,
      isRefresh: false,
      pageNo: 1,
      tabsState: 1, // 默认显示进行中状态
      tabsIndex: 0,
      taskTabs: [
        { state: 1, text: this.$t("task.tabs[0]") }, // 进行中
        { state: 2, text: this.$t("task.tabs[1]") }, // 审核中
        { state: 3, text: this.$t("task.tabs[2]") }, // 已完成
      ],
      fileList: [],
    };
  },
  computed: {
    UserInfo() {
      return this.$store.state.UserInfo || {};
    },

    InitData() {
      return this.$store.state.InitData || {};
    },

    // 网站标题
    webTitle() {
      return (this.InitData && this.InitData.setting && this.InitData.setting.web_title) || 'SmartNest';
    }
  },
  watch: {},
  created() {
    // 为每个标签页初始化空数组
    this.listData = this.taskTabs.map(() => []);
    // 根据传入的taskState参数设置初始标签页
    if (this.taskState) {
      const stateIndex = this.taskTabs.findIndex(tab => tab.state == this.taskState);
      if (stateIndex !== -1) {
        this.tabsIndex = stateIndex;
        this.tabsState = this.taskState;
      }
    } else {
      this.tabsState = this.taskTabs[0].state;
    }
    this.getListData("init");
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    onClickCell(id, event) {
      if (!$(event.target).hasClass("van-uploader__input")) {
        this.$router.push(`/user/taskInfo/${id}`);
      }
    },
    onLoad() {
      this.getListData("load");
    },
    changeTabs(index) {
      this.tabsIndex = index;
      this.tabsState = this.taskTabs[index].state;
      // 如果当前标签页还没有数据，则获取数据
      if (!this.listData[index] || this.listData[index].length === 0) {
        this.getListData("init");
      }
    },
    getListData(type) {
      this.isLoad = true;
      this.isRefresh = false;
      if (type == "load") {
        this.pageNo += 1;
      } else {
        this.pageNo = 1;
        this.isFinished = false;
      }
      this.$Model.GetTaskRecord(
        { status: this.tabsState, page_no: this.pageNo, is_u: 2 },
        (data) => {
          this.isLoad = false;
          if (data.code == 1) {
            if (type == "load") {
              // 确保listData[this.tabsIndex]是数组
              if (!Array.isArray(this.listData[this.tabsIndex])) {
                this.listData[this.tabsIndex] = [];
              }
              this.listData[this.tabsIndex] = this.listData[
                this.tabsIndex
              ].concat(data.info);
            } else {
              this.listData[this.tabsIndex] = data.info || [];
            }
            // 强制更新视图
            this.$forceUpdate();
            if (this.pageNo == data.data_total_page) {
              this.isFinished = true;
            } else {
              this.isFinished = false;
            }
          } else {
            this.listData[this.tabsIndex] = [];
            this.isFinished = true;
            this.$forceUpdate();
          }
        }
      );
    },
    onRefresh() {
      this.getListData("init");
    },
    afterRead(file) {
      if (this.InitData.setting.demo_img) {
        file.message = this.$t("upload[2]");
        file.status = "success";
        file.url =
          this.InitData.setting.up_url + this.InitData.setting.demo_img;
      } else {
        file.status = "uploading";
        file.message = this.$t("upload[0]");
        this.uploadImgs(file);
      }
    },
    compressImg(file) {
      this.$Util.CompressImg(file.file.type, file.content, 750, (image) => {
        let param = new FormData();
        param.append("token", localStorage["Token"]);
        param.append("type", 3);
        param.append("image", image, file.file.name);
        this.$Model.UploadImg(param, (data) => {
          if (data.code == 1) {
            file.message = this.$t("upload[2]");
            file.status = "success";
            file.url = data.url;
          } else {
            file.status = "failed";
            file.message = this.$t("upload[3]");
          }
        });
      });
    },
    uploadImgs(file) {
      if (file.length) {
        file.forEach((item) => {
          if (!item.file.type.match(/image/)) {
            item.status = "failed";
            item.message = this.$t("upload[1]");
            return;
          }
          this.compressImg(item);
        });
      } else {
        if (!file.file.type.match(/image/)) {
          file.status = "failed";
          file.message = this.$t("upload[1]");
          return;
        }
        this.compressImg(file);
      }
    },
    submitTask(id, index) {
      if (this.fileList[index]) {
        const examine_demo = this.fileList[index].flatMap((item) => item.url);
        this.$Model.SubmitTask(
          { order_id: id, examine_demo: examine_demo },
          (data) => {
            if (data.code == 1) {
              this.fileList[index] = [];
              this.getListData("init");
            }
          }
        );
      } else {
        this.$Dialog.Toast(this.$t("task.msg"));
      }
    },
    cancelTask(id, index) {
      this.$Model.SubmitTask({ order_id: id, status: 6 }, (data) => {
        if (data.code == 1) {
          this.fileList[index] = [];
          this.getListData("init");
        }
      });
    },
  },
};
</script>
<style scoped>
.IndexBox {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-top: 48px;
}
.IndexBox >>> .van-list__finished-text::before {
  background: none;
}
.van-tabs >>> .van-tab {
  padding: 0 10px;
}
.van-uploader {
  display: block;
  margin-top: 10px;
}
.van-uploader >>> .van-uploader__upload-icon {
  opacity: 0.3;
  font-size: 16px;
}
.van-uploader >>> .van-uploader__wrapper {
  justify-content: flex-end;
  flex-direction: column;
  align-items: flex-end;
}
.van-uploader >>> .van-uploader__upload,
.van-uploader >>> .van-uploader__preview-image {
  width: 36px;
  height: 36px;
  margin-right: 0;
  background-color: #0b273f;
}
.van-uploader >>> .van-uploader__preview {
  margin-right: 0;
}
.van-uploader >>> .van-uploader__mask-message {
  white-space: nowrap;
}
.van-pull-refresh {
  height: 100%;
  min-height: 100%;
}

.TaskItem {
  background-color: rgba(88, 190, 63, 0.1) !important;
  border-radius: 10px;
}

.TaskItem:not(:first-child) {
  margin-top: 15px;
}
.TaskItem .van-cell__value {
  overflow: visible;
}
.van-list {
  overflow: hidden;
  padding: 0px 20px;
}
.fixed-tabs-container >>> .van-tab--active {
  background-color: #ff0f23;
  color: #fff;
  border-radius: 50px;
  padding: 0 10px;
  width: 70px;
}
.TaskItem .van-cell__title {
  text-align: left;
  line-height: 1;
  margin-left: 0.9375rem;
  color: #292929;
  font-size: 12px;
}
.TaskItem .post > p,
.TaskItem .record > p {
  color: #2c2c2c;
  font-size: 1.1em;
}
.TaskItem .van-cell__value .price {
  color: #2c2c2c;
  line-height: 1;
  font-size: 0.75rem;
}

.TaskItem .van-cell__value .price p {
  color: #f59300;
}

/* 任务财务信息样式 */
.task-financial-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  margin-top: 4px;
  /* gap: 8px; */
  /* margin: 8px 0; */
  /* padding: 12px; */
  /* background: rgba(255, 255, 255, 0.8); */
  /* border-radius: 8px; */
  /* border-left: 4px solid #58be3f; */
}

.price-info,
.commission-info {
  display: flex;
  /* justify-content: space-between; */
  /* align-items: center; */
  font-size: 13px;
  line-height: 1.3;
}

.price-label,
.commission-label {
  color: #666666;
  font-weight: 500;
  font-size: 12px;
}

.price-value {
  color: #ff0f23;
  font-weight: 700;
  font-size: 15px;
}

.commission-value {
  color: #58be3f;
  font-weight: 700;
  font-size: 15px;
}

/* 已完成状态图标 */
.completed-status {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* .customPullRefresh {
  margin-top: 20px;
} */
.submitBtn {
  width: 60px;
  height: 30px;
  border-radius: 4px;
  background-color: #ff0f23;
  border: 1px solid #ff0f23;
}
/* 固定Tab栏样式 */
.fixed-tabs-container {
  flex-shrink: 0;
  background-color: #fff;
  z-index: 10;
  border-bottom: 1px solid #f0f0f0;
}

/* 可滚动内容区域样式 */
.scrollable-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tab-content {
  height: 100%;
  overflow-y: auto;
}

/* 自定义下拉刷新样式 */
.customPullRefresh {
  height: 100%;
  overflow-y: auto;
}

.IndexBox >>> .van-nav-bar__text {
  font-size: 24px;
  color: #292929;
  font-weight: 600;
}
</style>
