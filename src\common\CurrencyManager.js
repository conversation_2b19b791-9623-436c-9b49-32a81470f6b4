import store from '@/store'
import { NumberFormat } from '@formatjs/intl-numberformat'

/**
 * 币种管理器
 * 统一管理全局币种显示和数值格式化
 */
class CurrencyManager {
  constructor() {
    this.defaultCurrency = 'USDT';

    // 语言代码到标准locale的映射
    this.localeMap = {
      'cn': 'zh-CN',
      'ft': 'zh-TW',
      'en': 'en-US',
      'th': 'th-TH',
      'vi': 'vi-VN',
      'jp': 'ja-JP',
      'ma': 'ms-MY',
      'pt': 'pt-PT',
      'es': 'es-ES',
      'yd': 'hi-IN',
      'id': 'id-ID'
    };

    // 货币配置
    this.currencyConfig = {
      'USDT': { code: 'USD', symbol: 'USDT', position: 'before' },
      'USD': { code: 'USD', symbol: '$', position: 'before' },
      'EUR': { code: 'EUR', symbol: '€', position: 'before' },
      'CNY': { code: 'CNY', symbol: '¥', position: 'before' },
      'JPY': { code: 'JPY', symbol: '¥', position: 'before' },
      'THB': { code: 'THB', symbol: '฿', position: 'before' },
      'VND': { code: 'VND', symbol: '₫', position: 'after' },
      'MYR': { code: 'MYR', symbol: 'RM', position: 'before' },
      'IDR': { code: 'IDR', symbol: 'Rp', position: 'before' },
      'INR': { code: 'INR', symbol: '₹', position: 'before' }
    };
  }

  /**
   * 获取当前币种
   * 优先级：InitData.currency > 默认币种
   * @returns {string} 币种符号
   */
  getCurrency() {
    const initData = store.state.InitData;

    // 优先使用InitData中的currency
    if (initData && initData.currency) {
      return initData.currency;
    }

    // 如果没有，使用默认币种
    return this.defaultCurrency;
  }

  /**
   * 获取当前语言对应的locale
   * @returns {string} 标准locale代码
   */
  getCurrentLocale() {
    // 从i18n获取当前语言，如果没有则从localStorage获取
    let currentLang = 'en';

    try {
      // 尝试从全局i18n获取
      if (typeof window !== 'undefined' && window.$i18n) {
        currentLang = window.$i18n.locale;
      } else if (typeof localStorage !== 'undefined') {
        currentLang = localStorage.getItem('Language') || 'en';
      }
    } catch (error) {
      console.warn('无法获取当前语言，使用默认语言:', error);
    }

    return this.localeMap[currentLang] || 'en-US';
  }



  /**
   * 格式化金额显示（包含币种符号）- 兼容旧版本
   * @param {number|string} amount 金额
   * @param {number} decimals 小数位数，默认2位
   * @returns {string} 格式化后的金额字符串，包含币种
   */
  formatAmount(amount, decimals = 2) {
    return this.formatCurrency(amount, { decimals, showSymbol: true });
  }

  /**
   * 全球化数值简写格式化
   * @param {number|string} value 数值
   * @param {Object} options 格式化选项
   * @param {string} options.notation 记号类型: 'standard' | 'compact'
   * @param {string} options.compactDisplay 简写显示: 'short' | 'long'
   * @param {number} options.maximumFractionDigits 最大小数位数
   * @param {string} options.locale 指定locale，不传则使用当前语言
   * @returns {string} 格式化后的数值字符串
   */
  formatCompactNumber(value, options = {}) {
    const numValue = Number(value || 0);
    if (isNaN(numValue)) return '0';

    const {
      notation = 'compact',
      compactDisplay = 'short',
      maximumFractionDigits = 1,
      locale = null
    } = options;

    const targetLocale = locale || this.getCurrentLocale();

    try {
      // 使用现代浏览器的原生Intl.NumberFormat
      if (typeof Intl !== 'undefined' && Intl.NumberFormat) {
        const formatter = new Intl.NumberFormat(targetLocale, {
          notation,
          compactDisplay,
          maximumFractionDigits
        });
        return formatter.format(numValue);
      }

      // 降级到@formatjs/intl-numberformat
      const formatter = new NumberFormat(targetLocale, {
        notation,
        compactDisplay,
        maximumFractionDigits
      });
      return formatter.format(numValue);
    } catch (error) {
      console.warn('数值格式化失败，使用降级方案:', error);
      return this.fallbackFormatNumber(numValue, targetLocale);
    }
  }

  /**
   * 格式化金额显示（不包含币种符号）
   * @param {number|string} amount 金额
   * @param {number} decimals 小数位数，默认2位
   * @returns {string} 格式化后的金额字符串
   */
  formatAmountOnly(amount, decimals = 2) {
    const numAmount = Number(amount || 0);
    return numAmount.toFixed(decimals);
  }

  /**
   * 降级数值格式化方案
   * @param {number} value 数值
   * @param {string} locale 语言代码
   * @returns {string} 格式化后的字符串
   */
  fallbackFormatNumber(value, locale) {
    const absValue = Math.abs(value);

    // 根据locale选择不同的简写规则
    if (locale.startsWith('zh') || locale.startsWith('ja')) {
      // 中文/日文：万进制
      if (absValue >= 1e12) return (value / 1e12).toFixed(1) + '万亿';
      if (absValue >= 1e8) return (value / 1e8).toFixed(1) + '亿';
      if (absValue >= 1e4) return (value / 1e4).toFixed(1) + '万';
      if (absValue >= 1e3) return (value / 1e3).toFixed(1) + '千';
    } else if (locale.startsWith('hi')) {
      // 印地语：印度体系
      if (absValue >= 1e7) return (value / 1e7).toFixed(1) + 'Cr';
      if (absValue >= 1e5) return (value / 1e5).toFixed(1) + 'L';
      if (absValue >= 1e3) return (value / 1e3).toFixed(1) + 'K';
    } else {
      // 西方体系：千进制
      if (absValue >= 1e12) return (value / 1e12).toFixed(1) + 'T';
      if (absValue >= 1e9) return (value / 1e9).toFixed(1) + 'B';
      if (absValue >= 1e6) return (value / 1e6).toFixed(1) + 'M';
      if (absValue >= 1e3) return (value / 1e3).toFixed(1) + 'K';
    }

    return value.toString();
  }

  /**
   * 获取币种符号（用于模板中显示）
   * @returns {string} 币种符号
   */
  getSymbol() {
    return this.getCurrency();
  }

  /**
   * 格式化货币显示（包含币种，支持简写）
   * @param {number|string} amount 金额
   * @param {Object} options 格式化选项
   * @param {boolean} options.compact 是否使用简写格式
   * @param {number} options.decimals 小数位数
   * @param {boolean} options.showSymbol 是否显示货币符号
   * @returns {string} 格式化后的货币字符串
   */
  formatCurrency(amount, options = {}) {
    const {
      compact = false,
      decimals = 2,
      showSymbol = true
    } = options;

    const numAmount = Number(amount || 0);
    if (isNaN(numAmount)) return showSymbol ? `${this.getCurrency()}0.00` : '0.00';

    let formattedNumber;

    if (compact) {
      // 使用简写格式
      formattedNumber = this.formatCompactNumber(numAmount, {
        maximumFractionDigits: decimals
      });
    } else {
      // 使用标准格式
      formattedNumber = numAmount.toFixed(decimals);
    }

    // 添加货币符号
    if (showSymbol) {
      const currency = this.getCurrency();
      const config = this.currencyConfig[currency];

      if (config && config.position === 'after') {
        return `${formattedNumber}${currency}`;
      } else {
        return `${currency}${formattedNumber}`;
      }
    }

    return formattedNumber;
  }


}

// 创建单例实例
const currencyManager = new CurrencyManager();

export default currencyManager;
