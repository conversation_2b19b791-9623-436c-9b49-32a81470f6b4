<template>
  <div class="Site PageBox">
    <van-nav-bar
      fixed
      :border="false"
      :title="showInfo.title"
      left-arrow
      @click-left="$router.go(-1)"
    />
    <div class="ScrollBox">
      <div class="Content" style="padding: 10px 20px 20px" v-html="showInfo.content"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Info',
  components: {
  },
  props: ['articleType','articleId'],
  data() {
    return {
      showInfo: {
        title: ''
      },
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    if(this.articleType=='video'){
      this.showInfo = this.InitData.videovTutorial.find(item=>item.id==this.articleId)
    }
    if(this.articleType=='help'){
      this.showInfo = this.InitData.helpList.find(item=>item.id==this.articleId)
    }
    if(this.articleType=='notice'){
      this.showInfo = this.InitData.noticelist.find(item=>item.id==this.articleId)
    }
    if(this.articleType=='terms'){
      this.showInfo = this.InitData.disclaimerList.find(item=>item.id==this.articleId)
    }
  },
  mounted() {

  },
  activated() {

  },
  destroyed() {
    
  },
  methods: {
    
  }
}
</script>
<style scoped>
.Content>>>img{
  max-width: 100%;
}
</style>