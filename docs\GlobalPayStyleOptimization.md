# 全球支付页面样式优化

## 🎯 优化目标

针对全球支付页面的视觉效果和用户体验进行全面优化，提升界面的现代感和易用性。

## 📱 优化内容

### 1. **整体布局优化**

#### 页面容器
```css
.GlobalPay {
  padding: 20px 24px;           /* 增加左右边距 */
  background-color: #f7f8fa;
  padding-top: 110px;           /* 调整顶部间距 */
  min-height: 100vh;            /* 确保最小高度 */
}
```

#### 卡片容器
```css
.section {
  background: white;
  border-radius: 16px;          /* 更大的圆角 */
  padding: 24px;                /* 增加内边距 */
  margin-bottom: 20px;          /* 调整间距 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;    /* 添加边框 */
}
```

### 2. **国家选择区域优化**

#### 网格布局
```css
.country-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);  /* 固定2列布局 */
  gap: 16px;                              /* 增加间距 */
}
```

#### 国家卡片
```css
.country-item {
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  background: #fafafa;
  min-height: 120px;                      /* 固定最小高度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
```

#### 激活状态
```css
.country-item.active {
  border-color: #1989fa;
  background: linear-gradient(135deg, #e3f2fd 0%, #f0f9ff 100%);
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.2);
  transform: translateY(-2px);            /* 轻微上浮效果 */
}
```

#### 国家信息
```css
.country-flag {
  font-size: 32px;                        /* 更大的国旗图标 */
  margin-bottom: 12px;
  line-height: 1;
}

.country-name {
  font-size: 15px;
  font-weight: 600;                       /* 加粗字体 */
  margin-bottom: 6px;
  color: #1a1a1a;
  font-family: 'PingFang SC', sans-serif;
}

.country-currency {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}
```

### 3. **支付方式区域优化**

#### 支付方式列表
```css
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 16px;                              /* 增加间距 */
}
```

#### 支付方式卡片
```css
.payment-method-item {
  display: flex;
  align-items: center;
  padding: 20px;                          /* 增加内边距 */
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  background: #fafafa;
}
```

#### 支付方式图标
```css
.method-icon {
  width: 48px;                            /* 更大的图标 */
  height: 48px;
  margin-right: 16px;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

#### 支付方式信息
```css
.method-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #1a1a1a;
  font-family: 'PingFang SC', sans-serif;
}

.method-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}
```

### 4. **头部信息优化**

```css
.header-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);  /* 添加阴影 */
}
```

### 5. **底部操作区域优化**

```css
.payment-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);      /* 添加上阴影 */
}

.pay-button {
  height: 52px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
}
```

### 6. **交互效果优化**

#### 悬停效果
```css
.country-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-method-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
```

#### 按钮点击效果
```css
.pay-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.3);
}
```

### 7. **响应式设计**

```css
@media screen and (max-width: 375px) {
  .GlobalPay {
    padding: 16px 20px;
  }
  
  .section {
    padding: 20px;
  }
  
  .country-grid {
    gap: 12px;
  }
  
  .country-item {
    padding: 16px 12px;
    min-height: 100px;
  }
  
  .payment-method-item {
    padding: 16px;
  }
  
  .payment-actions {
    padding: 16px 20px;
  }
}
```

## 🎨 视觉改进

### 颜色方案
- **主色调**: #1989fa (蓝色)
- **背景色**: #f7f8fa (浅灰)
- **卡片背景**: #fafafa (更浅的灰)
- **边框色**: #f0f0f0 (淡灰)
- **文字色**: #1a1a1a (深黑)

### 阴影效果
- **卡片阴影**: `0 2px 12px rgba(0, 0, 0, 0.08)`
- **激活阴影**: `0 4px 12px rgba(25, 137, 250, 0.2)`
- **悬停阴影**: `0 4px 12px rgba(0, 0, 0, 0.1)`

### 圆角设计
- **主要卡片**: 16px
- **次要元素**: 12px
- **小元素**: 8px

## 📱 用户体验提升

1. **更清晰的视觉层次** - 通过不同的字体大小和颜色区分重要性
2. **更好的触摸体验** - 增加了卡片的最小高度和内边距
3. **流畅的交互反馈** - 添加了悬停和点击效果
4. **一致的设计语言** - 统一的圆角、间距和颜色使用
5. **响应式适配** - 在小屏幕设备上优化显示效果

## ✅ 优化效果

### 优化前的问题
- 国家卡片布局不够整齐
- 支付方式选择视觉层次不清晰
- 整体样式缺乏现代感
- 交互反馈不够明显

### 优化后的改进
- ✅ 国家选择采用固定2列网格布局，更加整齐
- ✅ 支付方式卡片增加了图标容器和更好的间距
- ✅ 添加了渐变背景和阴影效果，提升现代感
- ✅ 增加了悬停和点击效果，提供更好的交互反馈
- ✅ 统一了设计语言，提升整体一致性
- ✅ 优化了移动端显示效果

现在全球支付页面具有更好的视觉效果和用户体验！🎉
