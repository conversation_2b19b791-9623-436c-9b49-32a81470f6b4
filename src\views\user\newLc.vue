<template>
  <div class="PageBox">
    <div class="one">
      <h5><i>{{$t('newLc[0]')}}</i><span></span></h5>
      <p style="color: #000;">
        <img src="static/images/nav-009.58e733bf.png" alt="" srcset="">{{$t('newLc[1]')}}</p>
    </div>
    <div class="eee"></div>
    <div class="one two">
      <h4>{{$t('newLc[2]')}}</h4>
      <ul class="ulC">
        <li :class="{active:active == item.id}" @click="add(item)" v-for="(item,index) in radioHeader" :key="index">
          <span>{{item.title}}{{item.tiem}}{{$t('newLc[3]')}}</span><i>{{$t('newLc[4]')}}{{item.lilv*100}}%</i>
        </li>
      </ul>
    </div>
    <div class="eee"></div>
    <div class="one two">
      <h4>{{$t('newLc[5]')}}</h4>
      <div class="three" @click="inputa()"><i>{{$Currency.getSymbol()}}</i>
        <input v-model.trim="money" type="number" maxlength="8" :disabled="flag" :placeholder="$t('newLc[6]')" style="border:0;flex:1;background: transparent"><span>({{$t('newLc[7]')}}{{all.toFixed(2)}})</span>
      </div>
    </div>
    <div class="eee"></div>
    <div class="btn" @click="sub()">{{$t('newLc[8]')}}</div>
  </div>
</template>

<script>
  export default {
    name: 'newLc',
    components: {},
    props: [],
    data() {
      return {
        showHeader: !1,
        showPassWord: !1,
        showPayWord: !1,
        radioHeader: [],
        postData: {},
        active: 0,
        money: "",
        all: 0,
        lv: 0,
        day:0,
        flag: !0
      }
    },
    computed: {

    },
    watch: {
      money() {
        return this.all = this.money * this.lv * this.day
      }
    },
    created() {
      this.$parent.navBarTitle = this.$t('newLc[9]'),
        console.log(this.$parent),
        this.list()
    },
    mounted() {

    },
    activated() {

    },
    destroyed() {

    },
    methods: {
      sub() {
        if ("" == this.money || 0 == this.active)
          return void this.$Dialog.Toast(this.$t('newLc[10]'));
        let t = {
          userid: JSON.parse(localStorage.getItem("UserInfo")).userid,
          yuebaoid: this.active,
          money: this.money
        };
        this.$Model.newLcTj(t, t => {
          if (200 == t.errorCode)
            return this.$Dialog.Toast(t.successMsg),
              this.active = 0,
              this.flag = !0,
              this.money = "",
              void this.$router.push("/user/");
          this.$Dialog.Toast(t.errorMsg)
        })
      },
      inputa() {
        0 == this.active && this.$Dialog.Toast(this.$t('newLc[11]'))
      },
      add(t) {
          this.flag = !1,
          this.active = t.id,
          this.lv = t.lilv
          this.day = t.time
          this.all = this.money * this.lv * this.day
      },
      list() {
        this.$Model.newLc({}, t => {
          this.radioHeader = t.info
        })
      }
    }
  }
</script>
<style scoped>
  input:disabled {
      background: none
  }

  .btn {
      background: #3fadfa;
      color: #fff;
      text-align: center;
      line-height: 50px;
      border-radius: 50px;
      width: 92%;
      margin: 30px auto 0 auto
  }

  input::-webkit-input-placeholder {
      color: #999;
      font-weight: 400;
      font-size: 14px
  }

  input:-moz-placeholder,input::-moz-placeholder {
      color: #999;
      font-weight: 400;
      font-size: 14px
  }

  input:-ms-input-placeholder {
      color: #999;
      font-weight: 400;
      font-size: 14px
  }

  .three {
      padding-top: 20px;
      padding-bottom: 10px
  }

  .three i {
      font-size: 1rem
  }

  .three input {
      border: 0;
      font-weight: 400
  }

  .three span {
      color: #59a1dc;
      font-weight: 400
  }

  .one {
      padding-left: 10px;
      padding-right: 10px;
      padding-bottom: 10px
  }

  .PageBox {
      padding-top: 50px;
      position: relative
  }

  .PageBox h5 {
      text-align: center;
      padding: 10px 0;
      color: #969696;
      position: relative
  }

  .eee {
      background: #eee;
      height: 8px
  }

  .two {
      padding-top: 10px;
      font-size: 16px;
      font-weight: 700
  }

  .PageBox h5 span {
      width: 100%;
      position: absolute;
      left: 0;
      top: 50%;
      height: 1px;
      background: #eee;
      z-index: -1
  }

  .PageBox h5 i {
      background: #fff;
      width: 30%;
      display: inline-block
  }

  .PageBox p {
      display: flex;
      align-items: center;
      background: #f3f4f6;
      width: 45%;
      border-radius: 5px;
      padding: 15px;
      justify-content: center
  }

  .PageBox p img {
      width: 30px;
      height: 30px;
      margin-right: 5px
  }

  .ulC {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 20px;
      flex-wrap: wrap
  }

  .ulC li {
      border: 2px solid #b2b2b2;
      margin-bottom: 10px;
      border-radius: 8px;
      padding: 6px;
      width: 30%;
      box-sizing: border-box
  }

  .ulC li.active {
      border: 2px solid #4ca1da;
      border-radius: 8px;
      padding: 6px;
      width: 30%;
      background: #eff8ff;
      box-sizing: border-box;
      color: #4ca1da
  }

  .ulC li span {
      font-size: .8rem;
      display: block;
      text-align: center;
      margin-bottom: 5px
  }

  .ulC li i {
      font-size: .6rem;
      display: block;
      text-align: center
  }

  .ulC:after {
      content: "";
      width: 30%
  }

</style>
