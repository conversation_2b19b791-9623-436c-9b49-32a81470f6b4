<template>
  <div class="Site IndexBox">
    <div class="ScrollBox">
      <!-- 头部区域 -->
      <div class="header-section">


        <!-- 导航栏 -->
        <div class="nav-header">
          <div class="logoText">{{ webTitle }}</div>
          <div class="nav-actions">
            <div class="customer-service" @click="goToCustomerService">
              <svg class="service-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path d="M512 85.333333a341.376 341.376 0 0 1 338.688 298.666667H853.333333a85.333333 85.333333 0 0 1 85.12 78.933333L938.666667 469.333333v85.333334a85.333333 85.333333 0 0 1-78.933334 85.12L853.333333 640h-85.333333v-213.333333a256 256 0 1 0-512 0v213.333333a170.666667 170.666667 0 0 0 170.666667 170.666667h11.434666a85.333333 85.333333 0 1 1 0 85.376L426.666667 896a256 256 0 0 1-255.829334-246.4L170.666667 640a85.333333 85.333333 0 0 1-85.333334-85.333333v-85.333334a85.333333 85.333333 0 0 1 85.333334-85.333333l2.645333-0.042667A341.376 341.376 0 0 1 512 85.333333z m-149.333333 341.333334a64 64 0 1 1 0 128 64 64 0 0 1 0-128z m298.666666 0a64 64 0 1 1 0 128 64 64 0 0 1 0-128z" fill="currentColor"/>
              </svg>
            </div>
            <div class="language-switch" @click="goToLanguage">
              <svg class="language-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path d="M511.777943 68.191078c-245.204631 0-443.586864 198.60429-443.586864 443.808922s198.382233 443.808922 443.586864 443.808922 444.030979-198.60429 444.030979-443.808922S756.982574 68.191078 511.777943 68.191078zM819.11568 334.476841 688.191838 334.476841c-14.423501-55.476499-34.617425-108.733447-61.245899-157.995407C708.606797 204.440206 776.509303 261.025968 819.11568 334.476841zM512 158.506037c37.058011 53.256948 65.906036 112.505353 84.767616 175.96978L427.232384 334.475817C446.093964 271.01139 474.941989 211.762985 512 158.506037zM168.491459 600.76158c-7.322779-28.40391-11.538801-58.139142-11.538801-88.76158s4.216022-60.35767 11.538801-88.76158l149.785421 0c-3.550873 29.069059-5.991458 58.582233-5.991458 88.76158s2.440585 59.69252 6.213515 88.76158L168.491459 600.76158zM204.662263 689.523159l130.923842 0c14.423501 55.476499 34.617425 108.733447 61.245899 158.217465C315.171146 819.780829 247.267617 762.974032 204.662263 689.523159zM335.586105 334.476841 204.662263 334.476841c42.605354-73.449849 110.508883-130.257669 192.168718-158.217465C370.202507 225.743394 350.009605 279.000342 335.586105 334.476841zM512 865.493963c-36.835953-53.256948-65.683978-112.505353-84.767616-175.96978l169.535231 0C577.682955 752.987586 548.835953 812.235992 512 865.493963zM615.851253 600.76158 408.148747 600.76158c-4.216022-29.069059-7.100722-58.582233-7.100722-88.76158s2.8847-59.69252 7.100722-88.76158l207.702506 0c4.216022 29.069059 7.100722 58.582233 7.100722 88.76158S620.067274 571.69252 615.851253 600.76158zM627.167996 847.51959c26.628474-49.485041 46.821375-102.519931 61.245899-157.995407l130.923842 0C776.510326 762.974032 708.606797 819.558771 627.167996 847.51959zM705.500039 600.76158c3.550873-29.069059 6.213515-58.582233 6.213515-88.76158s-2.440585-59.69252-6.213515-88.76158l149.785421 0c7.322779 28.40391 11.760858 58.139142 11.760858 88.76158s-4.216022 60.35767-11.760858 88.76158L705.500039 600.76158z" fill="currentColor"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- 通知消息 -->
        <div class="notification-bar">
          <van-icon name="volume-o" class="notification-icon" />
          <span class="notification-text">{{ $t('home.notification', { count: 100 }) }}</span>
        </div>

        <!-- 轮播图 -->
        <van-swipe
          :autoplay="3000"
          indicator-color="#888"
          style="margin: 0 20px 16px; border-radius: 12px; height: 130px"
        >
          <van-swipe-item
            v-for="(item, index) in InitData.bannerList"
            :key="index"
          >
            <img :src="InitData.setting.up_url + item" style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;" />
          </van-swipe-item>
        </van-swipe>

        <!-- 流程说明 -->
        <div class="process-card">
          <div class="process-step">
            <div class="step-icon">
              <img src="@/static/images/Group 9226.png" :alt="$t('home.processSteps[0]')" />
            </div>
            <span>{{ $t('home.processSteps[0]') }}</span>
          </div>
          <div class="process-arrow">
            <img src="@/static/images/arrow.png" alt="→" />
          </div>
          <div class="process-step">
            <div class="step-icon">
              <img src="@/static/images/Group 9227.png" :alt="$t('home.processSteps[1]')" />
            </div>
            <span>{{ $t('home.processSteps[1]') }}</span>
          </div>
          <div class="process-arrow">
            <img src="@/static/images/arrow.png" alt="→" />
          </div>
          <div class="process-step">
            <div class="step-icon">
              <img src="@/static/images/Frame.png" :alt="$t('home.processSteps[2]')" />
            </div>
            <span>{{ $t('home.processSteps[2]') }}</span>
          </div>
          <div class="process-arrow">
            <img src="@/static/images/arrow.png" alt="→" />
          </div>
          <div class="process-step">
            <div class="step-icon">
              <img src="@/static/images/Group 92281.png" :alt="$t('home.processSteps[3]')" />
            </div>
            <span>{{ $t('home.processSteps[3]') }}</span>
          </div>
        </div>
      </div>

      <!-- 分类标签 -->
      <div class="category-tabs">
        <div class="tabs-container">
          <div class="tab-item" :class="{ active: activeTab === 'all' }" @click="onTabChange('all')">
            <span>{{ $t('home.allCategory') }}</span>
            <div class="tab-line" v-if="activeTab === 'all'"></div>
          </div>
          <div
            v-for="category in taskCategories"
            :key="category.group_id"
            class="tab-item"
            :class="{ active: activeTab === category.group_id }"
            @click="onTabChange(category.group_id)"
          >
            <span>{{ category.group_name }}</span>
            <div class="tab-line" v-if="activeTab === category.group_id"></div>
          </div>
        </div>

        <!-- 右侧排序按钮 -->
        <div class="sort-button-container">
          <div
            class="sort-button active"
            @click="onPriceSortToggle"
          >
            <span>{{ $t('home.priceSort') }}</span>
            <van-icon
              name="arrow-up"
              class="sort-icon"
              v-if="sortType === 'price_asc'"
            />
            <van-icon
              name="arrow-down"
              class="sort-icon"
              v-else-if="sortType === 'price_desc'"
            />
            <van-icon
              name="exchange"
              class="sort-icon"
              v-else
            />
          </div>
        </div>
      </div>

      <!-- 商品网格 -->
      <div class="product-grid">
        <van-pull-refresh v-model="isRefresh" @refresh="onRefresh">
          <van-list
            v-model="isLoad"
            :finished="isFinished"
            :finished-text="taskList.length ? $t('home.noMoreData') : $t('home.noTaskData')"
            @load="onLoad"
            :class="{ Empty: !taskList.length }"
          >
            <!-- 任务网格 -->
            <div class="grid-container">
              <div
                v-for="(item, index) in taskList"
                :key="index"
                class="product-card"
                @click="goTask(item.task_id)"
              >
                <div class="product-image">
                  <img :src="getProductImage(item)" @error="handleImageError" />
                </div>
                <div class="product-info">
                  <h4 class="product-title">{{ item.title || item.group_name || $t('home.defaultTaskTitle') }}</h4>
                  <div class="product-tags">
                    <span class="commission-tag">{{ $t('home.commissionTag') }}</span>
                    <span class="original-price">{{ InitData.currency }}{{ (Number(item.task_commission)).toFixed(2) || '0.00' }}</span>
                  </div>
                  <div class="buy-btn">
                    {{ InitData.currency }}{{ Number(item.purchase_price).toFixed(2) || '0.00' }}
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <!-- <van-cell class="Title" :title="$t('home.taskHall.title[1]')" :border="false" />
			<van-grid class="TaskHall" :class="$i18n.locale" direction="horizontal" :column-num="2" :border="false" gutter="5">
			  <van-grid-item v-for="(item,index) in InitData.taskclasslist.filter(obj=>obj.is_f==1)" :key="item.group_id" @click="goPost(item.group_id)">
			  	<template #icon>
			  		<h4>{{item.h_group_name}}</h4>
			  		{{item.h_group_info}}
			  	</template>
			  	<template #text>
			  		<img :src="InitData.setting.up_url+item.h_icon" style="width: 2.75rem">
			  	</template>
				</van-grid-item>
			</van-grid> -->
      <!-- <div class="Title">
        {{ $t("home.memberList.title") }}
      </div>
      <van-swipe
        id="SwipeList1"
        class="customSwipeClass"
        style="height: 340px"
        height="68"
        vertical
        autoplay="3000"
        :show-indicators="false"
        :touchable="false"
      >
        <van-swipe-item
          class="customSwipeItem"
          v-for="(item, index) in InitData.memberList"
          :key="index"
          :index="index"
        >
          <van-cell
            class="topItem"
            :icon="`./static/head/${item.header}`"
            :title="$t('home.memberList.data[0]', { member: item.username })"
            :label="$t('home.memberList.data[1]', { num: item.number })"
            center
          >
            <template slot="right-icon">
              <div class="profit">
                <img :src="`./static/icon/money.png`" style="height: 1.13rem" />
                <div class="profitText">{{ item.profit || "0.00" }}</div>
              </div>
            </template>
          </van-cell>
        </van-swipe-item>
      </van-swipe> -->

      <!-- <van-tab>
			  	<template #title>
			  		<van-icon :name="`./static/icon/tab2.png`" size="1.13rem" />{{$t('home.businessList.title')}}
			  	</template>
			  	<van-swipe id="SwipeList2" style="height: 340px" height="68" vertical autoplay="3000" :show-indicators="false" :touchable="false">
					  <van-swipe-item v-for="(item,index) in InitData.businessList" :key="index" :index="index">
					  	<van-cell
					  		class="topItem"
						  	:icon="`./static/head/${item.header}`"
						  	:title="item.username"
						  	:label="$t('home.businessList.data[1]',{ num: item.number})"
						  	center
					  	>
					  		<template slot="right-icon">
					  			<span class="profit">
					  				<img :src="`./static/icon/gold.png`" style="height: 1.13rem">
					  				{{item.profit||'0.00'}}
					  			</span>
					  		</template>
					  	</van-cell>
					  </van-swipe-item>
					</van-swipe>
			  </van-tab> -->
    </div>
    <Footer />
    <van-popup
      v-model="showNotice"
      style="background: transparent;width: 80%;text-align: center"
      @closed="$parent.isNotice = true"
    >
      <dl
        class="NoticePopup"
        v-if="InitData.noticelist && InitData.noticelist.length"
        @click="$router.push(`/article/notice/${InitData.noticelist[0].id}`)"
      >
        <dt>{{ $t("home.noticeTitle") }}</dt>
        <dd v-html="InitData.noticelist[0].content"></dd>
      </dl>
      <a class="close" href="javascript:;" @click="showNotice = false"
        ><van-icon name="clear" color="rgba(255,255,255,.8)" size="2.5rem"
      /></a>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: "Home",
  inject: ["reloadHtml"],
  components: {},
  props: [],

  data() {
    return {
      showNotice: false,
      lang: localStorage["Language"] || Language,
      activeTab: 'all',
      taskList: [], // 当前选中分类的任务列表
      sortType: 'price_asc', // 排序类型：price_asc, price_desc
      isLoading: false, // 加载状态
      currentBannerIndex: 0, // 当前轮播图索引
      // 分页相关状态
      isRefresh: false, // 下拉刷新状态
      isLoad: false, // 上拉加载状态
      isFinished: false, // 是否加载完毕
      pageNo: 1 // 当前页码
    };
  },

  computed: {
    // 网站标题
    webTitle() {
      return (this.InitData && this.InitData.setting && this.InitData.setting.web_title) || '';
    },
    // 任务分类列表
    taskCategories() {
      if (!this.InitData.taskclasslist) return [];
      return this.InitData.taskclasslist.filter(item => item.state == 1);
    },
    // 显示的任务数据（根据选中分类过滤）
    displayTasks() {
      return this.taskList;
    }
  },
  watch: {
    "InitData.noticelist"(data) {
      if (!this.$parent.isNotice && data.length) this.showNotice = true;
    },
    "InitData.taskclasslist"(data) {
      if (data && data.length) {
        this.getTaskList();
      }
    }
  },
  created() {
    // 初始化时获取任务列表
    this.$nextTick(() => {
      if (this.InitData && this.InitData.taskclasslist && this.InitData.taskclasslist.length) {
        this.getTaskList();
      }
    });
  },
  mounted() {
    if (this.InitData.memberList && this.InitData.memberList.length) {
      this.$nextTick(() => {
        const data = $("#SwipeList1 .van-swipe-item").slice(0, 5);
        for (var i = 0; i < data.length; i++) {
          $("#SwipeList1")
            .children()
            .append($(data[i])[0].outerHTML);
        }
      });
    }
    if (this.InitData.businessList && this.InitData.businessList.length) {
      this.$nextTick(() => {
        const data = $("#SwipeList2 .van-swipe-item").slice(0, 5);
        for (var i = 0; i < data.length; i++) {
          $("#SwipeList2")
            .children()
            .append($(data[i])[0].outerHTML);
        }
      });
    }
  },
  activated() {},
  destroyed() {},
  methods: {
    // 分类切换事件
    onTabChange(name) {
      this.activeTab = name;
      // 重置分页状态
      this.pageNo = 1;
      this.isFinished = false;
      this.taskList = [];
      // 重置排序状态
      this.sortType = 'price_asc';
      // 根据不同的标签加载不同的数据
      console.log('切换到标签:', name);
      this.getTaskList('init');
    },
    // 获取任务列表
    getTaskList(type = 'init') {
      if (this.isLoading && type !== 'refresh') return;

      this.isLoading = true;

      // 设置分页参数
      if (type === 'load') {
        this.pageNo += 1;
      } else {
        this.pageNo = 1;
        this.isFinished = false;
      }

      if (this.activeTab === 'all') {
        // 如果是"全部"分类，传入 group_id: 0
        this.getTasksByCategory(0, type);
      } else {
        // 获取特定分类的任务
        this.getTasksByCategory(this.activeTab, type);
      }
    },



    // 获取特定分类的任务
    getTasksByCategory(groupId, type = 'init') {
      const params = {
        group_id: groupId,
        page_no: this.pageNo,
        is_u: 0,
        sort_field: 'purchase_price',
        sort_type: this.sortType === 'price_asc' ? 'asc' : this.sortType === 'price_desc' ? 'desc' : 'asc'
      };

      this.$Model.GetTaskList(params, (data) => {
        this.isLoading = false;
        this.isLoad = false;
        this.isRefresh = false;

        if (data.code == 1) {
          if (type === 'load') {
            // 上拉加载：追加数据
            this.taskList = this.taskList.concat(data.info || []);
          } else {
            // 初始化或下拉刷新：替换数据
            this.taskList = data.info || [];
          }

          // 判断是否还有更多数据
          if (this.pageNo >= data.data_total_page) {
            this.isFinished = true;
          } else {
            this.isFinished = false;
          }
        } else {
          if (type !== 'load') {
            this.taskList = [];
            this.originalTaskList = [];
          }
          this.isFinished = true;
        }
      });
    },

    // 上拉加载更多
    onLoad() {
      this.getTaskList('load');
    },

    // 下拉刷新
    onRefresh() {
      this.getTaskList('refresh');
    },

    goTask(index) {
      this.$router.push(`/taskShow/${index}`);
    },



    // 价格排序切换（升序 -> 降序 -> 升序）
    onPriceSortToggle() {
      if (this.sortType === 'price_asc') {
        this.sortType = 'price_desc';
      } else {
        this.sortType = 'price_asc';
      }
      // 重新请求数据
      this.getTaskList('refresh');
    },


    getProductImage(item) {
      if (!item || !item.main_image) {
        return require('@/static/images/task-placeholder.svg');
      }

      // 如果已经是完整URL，直接返回
      if (item.main_image.startsWith('http')) {
        return item.main_image;
      }

      // 构建完整的图片URL
      const baseUrl = (this.InitData && this.InitData.setting && this.InitData.setting.up_url) || '';
      return baseUrl + item.main_image;
    },
    handleImageError(event) {
      // 防止死循环：如果已经是占位图了，就不再重新设置
      if (event.target.src.includes('task-placeholder.svg') || event.target.dataset.errorHandled) {
        return;
      }
      // 标记已经处理过错误
      event.target.dataset.errorHandled = 'true';
      // 图片加载失败时使用新的占位图
      event.target.src = require('@/static/images/task-placeholder.svg');
    },
    goPost(id) {
      this.$router.push({ name: "postTask", query: { type: id } });
    },

    // 轮播图切换事件
    onBannerChange(index) {
      this.currentBannerIndex = index;
    },
    goMiLiao() {
      this.$parent.showMiliao = true;
    },
    openVideo() {
      if (this.InitData.videovTutorial.length) {
        this.$router.push(
          `/article/video/${this.InitData.videovTutorial[0].id}`
        );
      } else {
        this.$Dialog.Toast(this.$t("home.video"));
      }
    },
    // 跳转到语言选择页面
    goToLanguage() {
      this.$router.push('/language');
    },
    // 跳转到客服页面
    goToCustomerService() {
      const serviceUrl = (this.InitData && this.InitData.setting && this.InitData.setting.service_url) || '/kefu/index.php?p=chat';
      window.open(serviceUrl, '_blank');
    },
  },
};
</script>
<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Source+Han+Sans+SC:wght@300;400;500;700;900&display=swap');

.IndexBox {
  background-color: #f4f4f4;
  font-family: 'Source Han Sans SC', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding-bottom: 70px !important;
}

/* 头部区域样式 */
.header-section {
  background: linear-gradient(180deg, #f42937 0%, rgba(244, 41, 55, 0) 100%);
  padding: 20px 0 20px 0;
}



/* 导航栏样式 */
.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 16px;
}

.logoText {
  color: #ffffff;
  font-size: 24px;
  font-weight: 900;
  font-family: 'Source Han Sans SC', sans-serif;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.customer-service {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 16px;
  transition: background-color 0.3s ease;
}

.customer-service:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.customer-service:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.service-icon {
  color: #ffffff;
  width: 20px;
  height: 20px;
  display: inline-block;
}

.language-switch {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 16px;
  transition: background-color 0.3s ease;
}

.language-switch:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.language-switch:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.language-icon {
  color: #ffffff;
  width: 20px;
  height: 20px;
  display: inline-block;
}



/* 通知栏样式 */
.notification-bar {
  background: rgba(255, 255, 255, 0.95);
  margin: 0 20px;
  padding: 12px 16px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.notification-icon {
  color: #f42937;
  margin-right: 8px;
  font-size: 16px;
}

.notification-text {
  color: rgba(0, 0, 0, 0.9);
  font-size: 14px;
}


/* 流程卡片样式 */
.process-card {
  background: white;
  margin: 0 24px;
  padding: 16px 16px;
  border-radius: 12px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.step-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.step-icon img {
  width: 22px;
  height: 20px;
  object-fit: contain;
}

.process-step span {
  font-size: 12px;
  color: #000000;
  text-align: center;
  font-weight: 400;
  line-height: 14px;
  font-family: 'Source Han Sans SC', sans-serif;
}

.process-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6px;
  flex-shrink: 0;
  width: 21px;
}

.process-arrow img {
  width: 21px;
  height: 21px;
  object-fit: contain;
}

/* 分类标签样式 */
.category-tabs {
  padding: 0 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tabs-container {
  display: flex;
  align-items: center;
  height: 32px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  flex: 1;
  margin-right: 16px;
}

.tabs-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.tab-item {
  position: relative;
  margin-right: 24px;
  cursor: pointer;
  height: 32px;
  display: flex;
  align-items: center;
  flex-shrink: 0; /* 防止项目被压缩 */
}

.tab-item:last-child {
  margin-right: 24px; /* 保持最后一个项目的右边距，确保滚动时有足够空间 */
}

.tab-item span {
  color: #1d2129;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
  line-height: 22px;
}

.tab-item.active span {
  color: #1d2129;
  font-weight: 700;
  font-family: 'Source Han Sans SC', sans-serif;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #ff4f01;
  border-radius: 17px;
}

/* 右侧排序按钮样式 */
.sort-button-container {
  flex-shrink: 0;
}

.sort-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  background: #f5f5f5;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  border: 1px solid transparent;
  height: 32px;
  box-sizing: border-box;
}

.sort-button.active {
  background: #ff0f23;
  color: #ffffff;
  border-color: #ff0f23;
  font-weight: 500;
}

.sort-button:hover:not(.active) {
  background: #e8e8e8;
  color: #333;
}

.sort-icon {
  font-size: 12px;
  margin-left: 2px;
}

/* 商品网格样式 */
.product-grid {
  padding: 0 16px;
}



/* 空状态样式 */
.product-grid >>> .van-list.Empty .van-list__finished-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}

.product-grid >>> .van-list.Empty .van-list__finished-text:before {
  content: "";
  width: 80px;
  height: 80px;
  background: url('~@/static/images/task-placeholder.svg') no-repeat center;
  background-size: contain;
  margin-bottom: 16px;
  opacity: 0.6;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.empty-content {
  text-align: center;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  color: #86909c;
  font-size: 14px;
  margin: 0;
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 12px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
  flex: 1 1 calc(50% - 6px);
  max-width: calc(50% - 6px);
  min-width: 160px;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image {
  width: 100%;
  height: 217px;
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 12px;
  background: white;
}

.product-title {
  font-size: 14px;
  color: #1d2129;
  margin: 0 0 8px 0;
  line-height: 22px;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
  min-height: 22px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.product-tags {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.commission-tag {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #ffffff;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  font-family: 'PingFang SC', sans-serif;
  line-height: 14px;
  height: 18px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
}

.original-price {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 16px;
  font-weight: 700;
  font-family: 'PingFang SC', sans-serif;
  line-height: 20px;
  text-shadow: 0 1px 3px rgba(255, 107, 53, 0.3);
}

.buy-btn {
  width: 100%;
  background: #ff0f23;
  border: none;
  border-radius: 6px;
  font-size: 18px;
  font-weight: 800;
  height: 40px;
  font-family: 'PingFang SC', sans-serif;
  line-height: 40px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(255, 15, 35, 0.3);
  transition: all 0.3s ease;
}

.buy-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(255, 15, 35, 0.4);
}
.NoticePopup {
  background: #fff url("/static/images/NoticePopup.png") no-repeat;
  background-size: 100%;
  border-radius: 24px;
}
.NoticePopup dt {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.NoticePopup dd {
  color: #292929;
  font-size: 16px;
  background-color: transparent;
}
</style>
