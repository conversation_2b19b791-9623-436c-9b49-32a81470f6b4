<template>
  <div class="PageBox">
    <div class="ScrollBox">
      <div>
        <div v-if="listData.length <= 0" style="text-align:center;">{{$t('newLcList[1]')}}</div>
        <div v-else>
          <van-cell class="FundItem" :border="false" v-for="(item,index) in listData" :key="item.dan">
            <template #title>
                <div><span class="money">{{$t('newLcList[2]')}}:{{Number(item.money) * item.lilv}}</span><span>{{$t('newLcList[3]')}}:{{item.money}}</span>
                </div>
                <div><span style="flex: 0 0 auto;">{{$t('newLcList[4]')}}:{{item.start_time}}</span><span style="color: green;">{{item.yuebaoid_name}}{{$t('newLcList[5]')}}:({{item.status_label}})</span>
                </div>
            </template>
          </van-cell>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'newLcList',
    components: {},
    props: [],
    data() {
      return {
        listData: "",
        isLoad: false,
        isFinished: false,
        isRefresh: false,
        pageNo: 1,
        tabsState: 4,
        tabsIndex: 0,
        taskTabs: [{
          state: 1,
          text: ""
        }]
      }
    },
    computed: {

    },
    watch: {

    },
    created() {
      this.$parent.navBarTitle = this.$t('newLcList[0]'),
        this.getListData("init")
    },
    mounted() {

    },
    activated() {

    },
    destroyed() {

    },
    methods: {
      onLoad() {
        this.getListData("load")
      },
      changeTabs(t) {},
      getListData(t) {
        this.isLoad = !0,
          this.isRefresh = !1,
          "load" == t ? this.pageNo += 1 : (this.pageNo = 1,
            this.isFinished = !1),
          this.$Model.newList({
            userid: JSON.parse(localStorage.getItem("UserInfo")).userid
          }, t => {
            this.isLoad = !1,
              this.listData = t.info
          })
      },
      onRefresh() {
        this.getListData("init")
      }
    }
  }
</script>
