# 全球化货币格式化使用指南

## 概述

项目已集成 `@formatjs/intl-numberformat` 库，支持全球化的数值和货币格式化，能够根据用户的语言设置自动显示符合当地习惯的数值简写。

## 支持的语言和格式

### 中文地区 (万进制)
- **简体中文 (cn)**: 1千, 1万, 1亿, 1万亿
- **繁体中文 (ft)**: 1千, 1萬, 1億, 1萬億
- **日语 (jp)**: 1千, 1万, 1億, 1兆

### 西方国家 (千进制)
- **英语 (en)**: 1K, 1M, 1B, 1T
- **西班牙语 (es)**: 1K, 1M, 1MM, 1B
- **葡萄牙语 (pt)**: 1K, 1M, 1B, 1T

### 印度体系 (特殊进制)
- **印地语 (yd)**: 1K, 1L (Lakh), 1Cr (Crore)

### 东南亚
- **泰语 (th)**: 1พัน, 1หมื่น, 1ล้าน
- **越南语 (vi)**: 1K, 1M, 1B (跟随西方)
- **马来语 (ma)**: 1K, 1J (Juta), 1B
- **印尼语 (id)**: 1Rb (Ribu), 1Jt (Juta), 1M (Miliar)

## 使用方法

### 1. 在Vue组件中使用 (推荐)

由于项目已全局混入 `CurrencyMixin`，所有组件都可以直接使用：

```javascript
// 基础数值简写
this.formatCompactNumber(1234567)
// 中文: "123万"
// 英文: "1.2M"

// 货币格式化 (不简写)
this.formatCurrency(1234.56)
// 返回: "USDT1234.56"

// 货币格式化 (简写)
this.formatCurrencyCompact(1234567.89)
// 中文: "USDT123万"
// 英文: "USDT1.2M"

// 兼容旧方法
this.formatLargeNumber(1234567)
// 自动使用全球化简写
```

### 2. 直接使用 CurrencyManager

```javascript
import CurrencyManager from '@/common/CurrencyManager'

// 数值简写
CurrencyManager.formatCompactNumber(1234567)

// 货币格式化
CurrencyManager.formatCurrency(1234.56, {
  compact: true,      // 使用简写
  decimals: 2,        // 小数位数
  showSymbol: true    // 显示货币符号
})
```

### 3. 高级选项

```javascript
// 自定义格式化选项
this.formatCompactNumber(1234567, {
  notation: 'compact',           // 'standard' | 'compact'
  compactDisplay: 'short',       // 'short' | 'long'
  maximumFractionDigits: 1,      // 最大小数位数
  locale: 'zh-CN'               // 强制指定语言
})

// 货币格式化选项
this.formatCurrencyCompact(1234567, {
  decimals: 1,        // 小数位数
  showSymbol: false   // 不显示货币符号
})
```

## 模板中使用示例

```vue
<template>
  <div>
    <!-- 团队人数 -->
    <span>{{ formatLargeNumber(teamCount) }}</span>
    
    <!-- 收益金额 -->
    <span>{{ InitData.currency }}{{ formatCurrency(earnings) }}</span>
    
    <!-- 简写货币 -->
    <span>{{ formatCurrencyCompact(largeAmount) }}</span>
    
    <!-- 自定义格式 -->
    <span>{{ formatCompactNumber(value, { maximumFractionDigits: 0 }) }}</span>
  </div>
</template>
```

## 测试工具

项目包含了测试工具，可以在浏览器控制台中测试各种语言的格式化效果：

```javascript
// 在浏览器控制台中运行
window.CurrencyFormatTest.runBrowserTest()
```

## 向后兼容性

- ✅ 所有旧的 `formatLargeNumber` 方法调用仍然有效
- ✅ 所有旧的 `formatCurrency` 方法调用仍然有效  
- ✅ 现有组件无需修改即可享受全球化支持
- ✅ 自动根据用户语言设置选择合适的格式

## 降级方案

如果浏览器不支持现代 `Intl.NumberFormat` 或 `@formatjs/intl-numberformat` 加载失败，系统会自动降级到内置的格式化方案，确保功能正常运行。

## 注意事项

1. **语言检测**: 系统会自动从 `localStorage['Language']` 获取当前语言设置
2. **性能**: 格式化器会被缓存，重复调用性能良好
3. **精度**: 使用标准的数值处理，避免浮点数精度问题
4. **扩展性**: 可以轻松添加新的语言和格式规则

## 常见问题

**Q: 如何添加新的语言支持？**
A: 在 `CurrencyManager.js` 的 `localeMap` 中添加语言映射即可。

**Q: 如何自定义某个语言的简写规则？**
A: 修改 `fallbackFormatNumber` 方法中的降级规则。

**Q: 为什么有些数值没有简写？**
A: 小于1000的数值默认不简写，这是国际惯例。
