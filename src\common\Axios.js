import axios from 'axios'
import store from '@/store'
import router from '@/router'
import { Toast,Dialog } from 'vant'
import i18n from '@/i18n'

//接口配置

var instance = axios.create({
  baseURL: (localStorage['CurrLine']||ApiUrl)+'/api/',
  headers: {
    'Content-Type':'application/x-www-form-urlencoded'
  }
})

/*取消请求*/
var source = axios.CancelToken.source();

// 添加请求拦截器
instance.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    if(!config.diyApi){
      config.baseURL = (localStorage['CurrLine']||ApiUrl)+'/api/';
    }else{
      config.baseURL = localStorage['UploadApi']+'/api/';
    }
    config.cancelToken = source.token;
    config.data = config.data || {};
    config.data.lang = localStorage['Language']|| Language;
    if(!config.noLogin){
      config.data.token = localStorage['Token'];
    }
    if(!config.fromData){
      config.data = $.param(config.data);
    }
    return config;
  },
  error => {
  // 对请求错误做些什么
  return Promise.reject(error);
  }
)

// 添加响应拦截器
instance.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    var Api = response.request.responseURL.slice(response.request.responseURL.lastIndexOf('\/'));
    if (response.data.code > 200 && response.data.code < 206) {
      localStorage.removeItem('Token');
      localStorage.removeItem('UserInfo');
      localStorage.removeItem('BankCardList');
      store.dispatch('UpdateUserInfo', '');
      store.dispatch('UpdateBankCardList', []);
      Toast.clear();
      if(router.history.current.name=='vip'){
        return response
      }
      source.cancel();
      Dialog.alert({
        title: i18n.t('dialog[0]'),
        message: response.data.code_dec,
        closeOnPopstate: true,
      }).then(() => {
        router.push('/login');
        source = axios.CancelToken.source();
      }).catch(()=>{
        router.push('/login');
        source = axios.CancelToken.source();
      });
    }
    return response;
  }, 
  error => {
    // 对响应错误做点什么
    let errorMessage = '';

    if (error.response && error.response.data) {
      // 检查是否是数据库错误
      if (typeof error.response.data === 'string' && error.response.data.includes('trial time')) {
        errorMessage = '系统维护中，请稍后再试';
      } else {
        // 尝试从HTML响应中提取错误信息
        const htmlMatch = /<h1>(.*?)<\/h1>/ig.exec(error.response.data);
        if (htmlMatch && htmlMatch[1]) {
          errorMessage = htmlMatch[1];
        } else {
          errorMessage = '网络请求失败，请稍后重试';
        }
      }
    } else {
      errorMessage = '网络连接失败，请检查网络设置';
    }

    Toast({message: errorMessage});
    return Promise.reject(error);
  }
)

export default instance