﻿<template>
  <div class="lottery-container">
    <!-- 背景图片层 -->
    <div class="background-image"></div>

    <!-- 透明导航栏 -->
    <div class="transparent-nav">
      <div class="nav-back" @click="$router.go(-1)">
        <van-icon name="arrow-left" />
      </div>
      <div class="nav-records" @click="goToRecords">
        <van-icon name="orders-o" />
        <span>{{ $t('wheelRecords.title') }}</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 剩余抽奖次数 -->
      <div class="lottery-count">
        <span>{{ $t('wheel[6]') }}{{ remainingTimes }}</span>
      </div>

      <!-- 抽奖网格 -->
      <div class="lottery-grid-container">
        <div class="lottery-grid">
        <!-- 动态渲染奖品配置 -->
        <div
          v-for="(prize, index) in gridPrizes"
          :key="index"
          class="grid-item"
          :class="[
            index === 4 ? 'center-button' : 'prize-item',
            { 'active': currentIndex === index && index !== 4 }
          ]"
          :data-index="index"
          @click="index === 4 ? startLottery() : null"
        >
          <!-- 奖品项 -->
          <template v-if="index !== 4">
            <div class="prize-icon">
              <img :src="getPrizeIcon(prize)" :alt="prize.name || prize.title" />
            </div>
            <div class="prize-text">{{ getPrizeContent(prize) }}</div>
          </template>
          <!-- 中心开始按钮 -->
          <template v-else>
            <div class="start-icon" :style="{ transform: `rotate(${buttonRotation}deg)` }">
              <img src="@/static/images/button.png" :alt="$t('wheel[7]')" />
            </div>
          </template>
        </div>
      </div>
      </div>

      <!-- 中奖记录 -->
      <div class="winner-records">
        <van-swipe height="22" style="height: 82px" vertical autoplay="3000" :show-indicators="false" :touchable="false">
          <van-swipe-item v-for="(item,index) in winers" :key="index" :index="index">
            <div class="record-item">
              <div class="winner-info">
                <span class="winner-phone">{{item.username}}{{ $t('wheel[4]') }}</span>
              </div>
              <div class="winner-prize">
                <span>{{item.prize}}</span>
              </div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>

      <!-- 抽奖须知 -->
      <div class="lottery-rules">
        <div class="rules-title">{{ $t('wheel[3]') }}</div>
        <div class="rules-content">
          <div class="remark-content" v-html="remark"></div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
  import '../../../static/js/jQueryRotate.js'
  export default {
    name: 'Wheel',
    components: {

    },
    props: [],
    data() {
      return {
        winers: [],
        remark: '',
        isSpinning: false,
        currentIndex: 0,
        buttonRotation: 315, // 中间指针角度，初始指向左上角
        remainingTimes: 0, // 剩余抽奖次数
        marqueeTimer: null, // 走马灯定时器
        marqueeIndex: 0, // 走马灯当前位置
        currentRotation: 315, // 当前实际旋转角度（用于累积计算）
        prizes: [
          { name: '一等奖', type: 'prize' },
          { name: '谢谢参与', type: 'thanks' },
          { name: '二等奖', type: 'prize' },
          { name: '谢谢参与', type: 'thanks' },
          { name: '一等奖', type: 'prize' },
          { name: '四等奖', type: 'prize' },
          { name: '谢谢参与', type: 'thanks' },
          { name: '四等奖', type: 'prize' }
        ]
      }
    },
    computed: {
      // 将奖品配置转换为9宫格布局（中心位置为开始按钮）
      gridPrizes() {
        const gridLayout = new Array(9);

        // 如果有API返回的奖品配置，使用API数据
        if (this.prizes && this.prizes.length > 0) {
          // 九宫格位置：0(左上) → 1(上) → 2(右上) → 3(左) → 4(中心) → 5(右) → 6(左下) → 7(下) → 8(右下)
          // 抽奖顺序：0 → 1 → 2 → 5 → 8 → 7 → 6 → 3 (跳过中心位置4)
          // 后台配置的奖品数组按抽奖顺序排列：prizes[0]对应位置0, prizes[1]对应位置1, prizes[2]对应位置2, prizes[3]对应位置5...
          const prizeSequence = [0, 1, 2, 5, 8, 7, 6, 3]; // 抽奖顺序对应的九宫格位置

          // 初始化所有位置
          for (let i = 0; i < 9; i++) {
            if (i === 4) {
              // 中心位置保留给开始按钮
              gridLayout[i] = { name: '开始', type: 'start' };
            } else {
              gridLayout[i] = { name: '谢谢参与', type: '0' }; // 默认值
            }
          }

          // 按照抽奖顺序填充奖品
          for (let sequenceIndex = 0; sequenceIndex < prizeSequence.length && sequenceIndex < this.prizes.length; sequenceIndex++) {
            const gridPosition = prizeSequence[sequenceIndex]; // 九宫格中的实际位置
            const prize = this.prizes[sequenceIndex]; // 后台配置中的奖品
            if (prize) {
              gridLayout[gridPosition] = prize;
            }
          }
        } else {
          // 使用默认配置
          gridLayout[0] = { name: '一等奖', type: 'prize' };
          gridLayout[1] = { name: '谢谢参与', type: 'thanks' };
          gridLayout[2] = { name: '二等奖', type: 'prize' };
          gridLayout[3] = { name: '谢谢参与', type: 'thanks' };
          gridLayout[4] = { name: '开始', type: 'start' };
          gridLayout[5] = { name: '四等奖', type: 'prize' };
          gridLayout[6] = { name: '谢谢参与', type: 'thanks' };
          gridLayout[7] = { name: '四等奖', type: 'prize' };
          gridLayout[8] = { name: '一等奖', type: 'prize' };
        }

        console.log('=== 奖品配置映射调试 ===');
        console.log('后台奖品配置:', this.prizes);
        console.log('九宫格布局:', gridLayout);
        console.log('抽奖顺序:', [0, 1, 2, 5, 8, 7, 6, 3]);
        console.log('========================');
        return gridLayout;
      }
    },
    watch: {

    },
    created() {
      this.$parent.navBarTitle = this.$t('wheel[5]')
      this.list()
      this.getPrizes()
      this.getRemainingTimes()
    },
    mounted() {
      // 网格抽奖不需要 jQuery 转盘初始化
      this.startMarquee(); // 启动走马灯效果
      this.addVisibilityListener(); // 添加页面可见性监听
    },
    activated() {
      // 页面激活时重新启动走马灯
      this.startMarquee();
    },
    destroyed() {
      this.stopMarquee(); // 清理走马灯定时器
      this.removeVisibilityListener(); // 移除页面可见性监听
    },
    methods: {
      // 跳转到抽奖记录页面
      goToRecords() {
        this.$router.push('/user/wheelRecords');
      },

      // 计算最短旋转路径
      calculateRotation(targetAngle) {
        const currentAngle = this.currentRotation % 360;
        let diff = targetAngle - currentAngle;

        // 确保旋转角度在-180到180之间（最短路径）
        if (diff > 180) {
          diff -= 360;
        } else if (diff < -180) {
          diff += 360;
        }

        this.currentRotation += diff;
        this.buttonRotation = this.currentRotation;
      },

      // 启动走马灯效果
      startMarquee() {
        if (this.isSpinning) return; // 抽奖时不启动走马灯

        this.stopMarquee(); // 先清理之前的定时器

        // 启用走马灯时的平滑过渡动画
        const startIcon = document.querySelector('.center-button .start-icon');
        if (startIcon) {
          startIcon.classList.remove('no-transition');
          startIcon.classList.add('smooth-transition');
        }

        const gridSequence = [0, 1, 2, 5, 8, 7, 6, 3]; // 九宫格顺序
        const pointerAngles = [315, 0, 45, 90, 135, 180, 225, 270]; // 对应角度

        this.marqueeTimer = setInterval(() => {
          if (this.isSpinning) return; // 抽奖时暂停走马灯

          // 清除所有高亮
          const items = document.querySelectorAll('.grid-item');
          items.forEach(item => item.classList.remove('active'));

          // 高亮当前位置（跳过中心按钮）
          const currentIndex = gridSequence[this.marqueeIndex];
          if (items[currentIndex] && currentIndex !== 4) {
            items[currentIndex].classList.add('active');
          }

          // 走马灯状态下指针指向当前位置（使用最短路径旋转）
          this.calculateRotation(pointerAngles[this.marqueeIndex]);

          // 移动到下一个位置
          this.marqueeIndex = (this.marqueeIndex + 1) % 8;
        }, 1500); // 每1.5秒移动一次
      },

      // 停止走马灯效果
      stopMarquee() {
        if (this.marqueeTimer) {
          clearInterval(this.marqueeTimer);
          this.marqueeTimer = null;
        }
        // 清除走马灯高亮
        const items = document.querySelectorAll('.grid-item');
        items.forEach(item => item.classList.remove('active'));
      },

      // 添加页面可见性监听
      addVisibilityListener() {
        this.handleVisibilityChange = () => {
          if (document.hidden) {
            // 页面进入后台，暂停走马灯
            this.stopMarquee();
          } else {
            // 页面回到前台，重新启动走马灯（如果没有在抽奖）
            if (!this.isSpinning) {
              this.startMarquee();
            }
          }
        };

        document.addEventListener('visibilitychange', this.handleVisibilityChange);
      },

      // 移除页面可见性监听
      removeVisibilityListener() {
        if (this.handleVisibilityChange) {
          document.removeEventListener('visibilitychange', this.handleVisibilityChange);
          this.handleVisibilityChange = null;
        }
      },

      startLottery() {
        if (this.isSpinning) return;

        // 检查剩余抽奖次数
        if (this.remainingTimes <= 0) {
          this.$Dialog.Toast(this.$t('wheel[8]'));
          return;
        }

        // 停止走马灯效果
        this.stopMarquee();

        this.isSpinning = true;

        // 抽奖过程中禁用过渡动画，使按钮直接跳转角度
        const startIcon = document.querySelector('.center-button .start-icon');
        if (startIcon) {
          startIcon.classList.remove('smooth-transition');
          startIcon.classList.add('no-transition');
        }

        // 调用原有的抽奖接口
        this.$Model.addWheelReward({}, data => {
          console.log('抽奖接口返回:', data);
          if (data.code == 1) {
            let targetIndex = 0;
            if (Number(data.order) > 0) {
              // data.order 是奖品在后台配置中的顺序（1-8），对应 prizes 数组的索引
              // 抽奖顺序对应的九宫格位置：[0, 1, 2, 5, 8, 7, 6, 3]
              const prizeSequence = [0, 1, 2, 5, 8, 7, 6, 3];
              const sequenceIndex = Number(data.order) - 1; // 转换为0-based索引
              targetIndex = prizeSequence[sequenceIndex] || 0; // 获取对应的九宫格位置
              console.log(`中奖了！后台奖品顺序: ${data.order}, 数组索引: ${sequenceIndex}, 九宫格位置: ${targetIndex}`);
            } else {
              // 谢谢参与，随机选择一个谢谢参与的位置
              const thankPositions = [1, 3, 6]; // 根据实际配置调整
              targetIndex = thankPositions[this.randomnum(0, thankPositions.length - 1)];
              console.log(`谢谢参与，九宫格位置: ${targetIndex}`);
            }

            this.runGridAnimation(targetIndex, () => {
              // 简化逻辑：所有奖励都是系统直接发送
              if (Number(data.order) > 0) {
                // 有中奖，根据奖品类型判断
                const prizeType = parseInt(data.type);

                if (prizeType === 0) {
                  // type: 0 = 谢谢参与类型
                  this.$Dialog.Toast(this.$t('wheel[2]')); // "再接再厉,中奖就在下一次"
                } else {
                  // type: 1 = 任务类型, type: 2 = 金钱类型，都是系统直接发放
                  this.$Dialog.Toast(this.$t('wheel[0]', {
                    name: data.name
                  })); // "恭喜你中奖了{name},系统已将奖励派送"
                }
              } else {
                // data.order <= 0，谢谢参与
                this.$Dialog.Toast(this.$t('wheel[2]')); // "再接再厉,中奖就在下一次"
              }
              this.isSpinning = false;
              // 抽奖结束后重新启用过渡动画
              const startIcon = document.querySelector('.center-button .start-icon');
              if (startIcon) {
                startIcon.classList.remove('no-transition');
                startIcon.classList.add('smooth-transition');
              }
              // 抽奖完成后重新获取剩余次数
              this.getRemainingTimes();
              // 延时获取最新的中奖消息，确保数据已插入数据库
              setTimeout(() => {
                this.list();
              }, 500); // 500ms后获取最新中奖消息
              // 重新启动走马灯效果
              setTimeout(() => {
                this.startMarquee();
              }, 2000); // 2秒后重新启动走马灯
            });
          } else {
            this.$Dialog.Toast(data.code_dec);
            this.isSpinning = false;
            // 抽奖失败后重新启用过渡动画
            const startIcon = document.querySelector('.center-button .start-icon');
            if (startIcon) {
              startIcon.classList.remove('no-transition');
              startIcon.classList.add('smooth-transition');
            }
            // 重新启动走马灯效果
            setTimeout(() => {
              this.startMarquee();
            }, 2000); // 2秒后重新启动走马灯
          }
        });
      },

      runGridAnimation(targetIndex, callback) {
        const items = document.querySelectorAll('.grid-item');
        let currentStep = 0;

        // 九宫格围绕中心的顺序：0→1→2→5→8→7→6→3→0...
        const gridSequence = [0, 1, 2, 5, 8, 7, 6, 3];

        // 找到目标位置在序列中的索引
        const targetSequenceIndex = gridSequence.indexOf(targetIndex);
        if (targetSequenceIndex === -1) {
          console.error('目标位置不在有效序列中:', targetIndex);
          callback();
          return;
        }

        const totalSteps = 16 + targetSequenceIndex; // 转两圈再停在目标位置

        const animate = () => {
          // 清除所有高亮
          items.forEach(item => item.classList.remove('active'));

          // 高亮当前位置（跳过中心按钮）
          const sequenceIndex = currentStep % 8;
          const currentIndex = gridSequence[sequenceIndex];
          if (currentIndex !== 4) {
            items[currentIndex].classList.add('active');
          }

          // 抽奖过程中使用累积角度计算，避免315度到0度的反跳
          const pointerAngles = [315, 0, 45, 90, 135, 180, 225, 270];
          const targetAngle = pointerAngles[sequenceIndex];

          // 计算累积旋转角度，避免反跳
          const currentAngle = this.currentRotation % 360;
          let diff = targetAngle - currentAngle;

          // 确保旋转方向正确（顺时针）
          if (diff < 0) {
            diff += 360;
          }

          this.currentRotation += diff;
          this.buttonRotation = this.currentRotation;

          currentStep++;

          if (currentStep < totalSteps) {
            const delay = currentStep < totalSteps - 8 ? 100 : 200; // 最后一圈减速
            setTimeout(animate, delay);
          } else {
            setTimeout(() => {
              items.forEach(item => item.classList.remove('active'));
              // 最终位置也使用累积角度计算
              const finalTargetAngle = pointerAngles[targetSequenceIndex];
              const finalCurrentAngle = this.currentRotation % 360;
              let finalDiff = finalTargetAngle - finalCurrentAngle;

              if (finalDiff < 0) {
                finalDiff += 360;
              }

              this.currentRotation += finalDiff;
              this.buttonRotation = this.currentRotation;
              callback();
            }, 500);
          }
        };

        // 立即开始第一帧动画，确保从位置0开始
        animate();
      },

      list() {
        this.$Model.getWheelWiner({}, t => {
          console.log('获取中奖记录响应:', t);
          if (t.code == 1) {
            this.winers = t.winers || [];
            this.remark = t.remark || '';
          } else {
            console.log('获取中奖记录失败:', t.code_dec);
            this.winers = [];
            this.remark = '';
          }
        })
      },
      // 根据奖品类型获取图标
      getPrizeIcon(prize) {
        if (!prize) return this.getIconPath('fonts.png');

        // 如果奖品有图标字段，直接使用
        if (prize.icon) return prize.icon;

        // 根据API返回的type字段判断
        const type = parseInt(prize.type);

        switch(type) {
          case 0:
            // type: 0 = 谢谢参与类型
            return this.getIconPath('fonts.png');
          case 1:
            // type: 1 = 任务类型
            return this.getIconPath('number.png');
          case 2:
            // type: 2 = 金钱类型
            return this.getIconPath('money.png');
          default:
            // 默认谢谢参与
            return this.getIconPath('fonts.png');
        }
      },
      // 获取图标路径
      getIconPath(iconName) {
        return require(`@/static/images/${iconName}`);
      },
      // 获取奖品显示内容
      getPrizeContent(prize) {
        if (!prize) return '';

        const type = parseInt(prize.type);

        switch(type) {
          case 0:
            // type: 0 = 谢谢参与，不显示文字
            return '';
          case 1:
            // type: 1 = 任务类型，显示"任务+次数"
            if (prize.num && prize.num > 0) {
              return `任务${prize.num}次`;
            }
            return '任务';
          case 2:
            // type: 2 = 金钱类型，只显示金额数字
            if (prize.cash_amount && parseFloat(prize.cash_amount) > 0) {
              return `${prize.cash_amount}`;
            }
            return '100';
          default:
            // 默认不显示
            return '';
        }
      },
      // 获取奖品配置
      getPrizes() {
        this.$Model.getWheelPrizes({}, data => {
          console.log('获取奖品配置响应:', data);
          if (data.code == 1) {
            // 修复数据访问路径：检查 data.data.prizes 或 data.prizes
            const prizes = (data.data && data.data.prizes) || data.prizes;
            if (prizes && prizes.length > 0) {
              this.prizes = prizes;
              console.log('奖品配置获取成功:', this.prizes);
            } else {
              console.log('API返回的奖品配置为空，使用默认配置');
            }
          } else {
            console.log('奖品配置获取失败:', data.code_dec || '未知错误');
          }
        })
      },
      // 获取剩余抽奖次数
      getRemainingTimes() {
        this.$Model.getWheelRemainingTimes({}, data => {
          console.log('获取剩余抽奖次数响应:', data);
          if (data.code == 1) {
            // 修复数据访问路径：data.data.remaining_times
            this.remainingTimes = (data.data && data.data.remaining_times) || 0;
            console.log('剩余抽奖次数获取成功:', this.remainingTimes);
          } else {
            console.log('剩余抽奖次数获取失败:', data.code_dec);
            this.remainingTimes = 0;
          }
        })
      },
      randomnum(smin, smax) {
        var Range = smax - smin;
        var Rand = Math.random();
        return (smin + Math.round(Rand * Range));
      },
      runzp(rate_order) {
        let order = rate_order;
        const rate = [{
            order: "1",
            angle: [345, 385]
          },
          {
            order: "2",
            angle: [16, 56]
          },

          {
            order: "3",
            angle: [57, 97]
          },
          {
            order: "4",
            angle: [98, 138]
          },

          {
            order: "5",
            angle: [139, 179]
          },
          {
            order: "6",
            angle: [180, 220]
          },
          {
            order: "7",
            angle: [221, 261]
          },
          {
            order: "8",
            angle: [262, 302]
          },
          {
            order: "9",
            angle: [303, 344]
          }

        ]
        let angle0 = [];
        for (let x = 0; x < rate.length; x++) {
          if (rate[x].order == order) {
            angle0 = rate[x].angle;
          }
        }
        var r0 = this.randomnum(angle0[0], angle0[1]);
        var angle = r0;


        var myreturn = new Object;

        myreturn.angle = angle;
        myreturn.message = "111";
        myreturn.prize = "222";
        return myreturn;
      }
    }
  }
</script>
<style scoped>
.lottery-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #ff8340 0%, #f4313e 100%);
  position: relative;
}

/* 背景图片层 */
.background-image {
  position: absolute;
  top: 22%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 393px;
  height: 311px;
  background: url('~@/static/images/vip_new_bg.png') no-repeat center center;
  background-size: cover;
  opacity: 0.3;
  z-index: 1;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 60px 24px 24px;
  gap: 32px;
  position: relative;
  z-index: 2;
}

/* 透明导航栏 */
.transparent-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 10;
}

.nav-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  cursor: pointer;
}

.nav-back .van-icon {
  color: white;
  font-size: 20px;
}

.nav-records {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.nav-records:hover {
  background: rgba(0, 0, 0, 0.2);
}

.nav-records .van-icon {
  color: white;
  font-size: 16px;
  margin-right: 4px;
}

.nav-records span {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

/* 剩余抽奖次数 */
.lottery-count {
  align-self: center;
  background: #f4333e;
  color: white;
  padding: 8px 24px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 500;
}

/* 装饰色块 */
.decoration-block {
  align-self: center;
  width: 320px;
  height: 16px;
  background: #F53B00;
  border-radius: 16px 16px 0 0;
  margin-bottom: -16px;
  z-index: 3;
  position: relative;
}

/* 抽奖网格容器 */
.lottery-grid-container {
  align-self: center;
  width: 320px;
  height: 320px;
  flex-shrink: 0;
  margin-top: 0;
}

.lottery-grid {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8px;
  background: #FFC765;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}
.lottery-grid::after{
  position: absolute;
  width: calc(100% - 32px);
  height: calc(100% - 32px);
  border-radius: 16px;
  background: #F53B00;
  content: '';
  top: 16px;
  left: 16px;
  z-index: -1;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.grid-item.active {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.3);
  border: 2px solid #ffffff;
}

.prize-item {
  background: url('~@/static/images/iconbg.png') no-repeat center center;
  background-size: cover;;
}

.prize-item.active {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8340 100%);
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4);
}

.prize-item .prize-icon {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prize-icon img {
  width: 36px;
  height: 36px;
  object-fit: contain;
}

.prize-item .prize-text {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.center-button {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8340 100%);
  border: 3px solid #fff;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
}

.center-button:hover {
  transform: scale(1.05);
}

.center-button .start-icon {
  font-size: 32px;
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-button .start-icon {
  transform-origin: center 54%; /* 旋转中心点向下偏移 */
}

/* 只在走马灯状态下使用过渡动画 */
.center-button .start-icon.smooth-transition {
  transition: transform 0.3s ease-in-out;
}

/* 抽奖状态下不使用过渡动画 */
.center-button .start-icon.no-transition {
  transition: none;
}

.center-button .start-icon img {
  width: 75px;
  object-fit: contain;
}

.center-button .start-text {
  font-size: 14px;
  font-weight: 600;
  color: white;
}



/* 底部导航 */
.bottom-navigation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 34px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-indicator {
  width: 135px;
  height: 5px;
  background: white;
  border-radius: 100px;
}

/* 中奖记录 */
.winner-records {
  height: 106px;
  background: #795ce7;
  border-radius: 8px;
  padding: 12px;
  margin-top: auto;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 20px;
  width: 100%;
  margin-bottom: 2px;
}

.winner-info {
  flex: 1;
}

.winner-phone {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}

.winner-prize {
  flex: 1;
  text-align: right;
}

.winner-prize span {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #ffe6e6;
}

/* 抽奖须知 */
.lottery-rules {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  min-height: 120px;
}

.rules-title {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  margin-bottom: 12px;
}

.rules-content {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
  text-align: left;
}

.rule-item {
  margin-bottom: 6px;
  line-height: 18px;
}

.remark-content {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
  text-align: left;
  white-space: pre-line;
}


</style>
