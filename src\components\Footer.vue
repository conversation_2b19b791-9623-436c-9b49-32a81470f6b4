<template>
  <van-tabbar
    v-model="tabbarIndex"
    safe-area-inset-bottom
    :border="false"
    inactive-color="#AAAAAA"
    active-color="#ff0f23"
    z-index="99"
    :fixed="true"
    class="custom-tabbar"
  >
    <van-tabbar-item to="/" name="home">
      <template #icon="props">
        <img :src="props.active ? icon_nav1.active : icon_nav1.normal" />
      </template>
      {{ $t('footer[0]') }}
    </van-tabbar-item>
    <van-tabbar-item to="/myTask" name="myTask">
      <template #icon="props">
        <img :src="props.active ? icon_nav2.active : icon_nav2.normal" />
      </template>
      {{ $t('footer[1]') }}
    </van-tabbar-item>
    <van-tabbar-item to="/vip" name="vip">
      <template #icon="props">
        <img :src="props.active ? icon_nav4.active : icon_nav4.normal" />
      </template>
      {{ $t('footer[3]') }}
    </van-tabbar-item>
    <van-tabbar-item to="/user/teamReport" name="team">
      <template #icon="props">
        <img :src="props.active ? icon_nav6.active : icon_nav6.normal" />
      </template>
      {{ $t('home.teamTab') }}
    </van-tabbar-item>
    <van-tabbar-item to="/user" name="user">
      <template #icon="props">
        <img :src="props.active ? icon_nav5.active : icon_nav5.normal" />
      </template>
      {{ $t('footer[4]') }}
    </van-tabbar-item>
  </van-tabbar>
</template>

<script>
export default {
  name: "Footer",
  components: {},
  props: [],
  data() {
    return {
      tabbarIndex: this.$route.meta.active || this.$route.matched[this.$route.matched.length - 1].meta.active,
      icon_nav1: {
        normal: "./static/icon/index.png",
        active: "./static/icon/index_select.png",
      },
      icon_nav2: {
        normal: "./static/icon/task.png",
        active: "./static/icon/task_select.png",
      },
      icon_nav3: {
        normal: "./static/icon/miliao.png",
        active: "./static/icon/miliao_select.png",
      },
      icon_nav4: {
        normal: "./static/icon/vip.png",
        active: "./static/icon/vip_select.png",
      },
      icon_nav5: {
        normal: "./static/icon/user.png",
        active: "./static/icon/user_select.png",
      },
      icon_nav6: {
        normal: "./static/icon/Peoples.png",
        active: "./static/icon/Peoples_select.png",
      },
    };
  },
  computed: {},
  watch: {
    '$route'(to) {
      this.tabbarIndex = to.meta.active || to.matched[to.matched.length - 1].meta.active;
    }
  },
  created() {},
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {},
};
</script>
<style scoped>
.custom-tabbar {
  height: 60px;
  background: white;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.custom-tabbar >>> .van-tabbar-item {
  padding: 6px 0 8px;
}

.custom-tabbar >>> .van-tabbar-item__icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.custom-tabbar >>> .van-tabbar-item__icon img {
  width: 20px;
  height: 20px;
}

.custom-tabbar >>> .van-tabbar-item__text {
  font-size: 12px;
  line-height: 1.2;
}

.custom-tabbar >>> .van-tabbar-item--active {
  color: #ff0f23;
}

.custom-tabbar >>> .van-tabbar-item--active .van-tabbar-item__icon {
  color: #ff0f23;
}
</style>
