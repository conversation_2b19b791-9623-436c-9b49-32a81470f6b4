// WatchPay支付模拟数据
export const mockCountries = [
  {
    code: "ID",
    name: "Indonesia",
    name_cn: "印度尼西亚",
    flag: "🇮🇩",
    currency: "IDR",
    min_amount: 50000,
    max_amount: 50000000,
    fee_rate: 2.0
  },
  {
    code: "IN",
    name: "India",
    name_cn: "印度",
    flag: "🇮🇳",
    currency: "INR",
    min_amount: 100,
    max_amount: 100000,
    fee_rate: 2.5
  },
  {
    code: "TH",
    name: "Thailand",
    name_cn: "泰国",
    flag: "🇹🇭",
    currency: "THB",
    min_amount: 100,
    max_amount: 100000,
    fee_rate: 2.0
  },
  {
    code: "VN",
    name: "Vietnam",
    name_cn: "越南",
    flag: "🇻🇳",
    currency: "VND",
    min_amount: 100000,
    max_amount: 100000000,
    fee_rate: 2.0
  },
  {
    code: "MY",
    name: "Malaysia",
    name_cn: "马来西亚",
    flag: "🇲🇾",
    currency: "MYR",
    min_amount: 10,
    max_amount: 10000,
    fee_rate: 2.0
  },
  {
    code: "BR",
    name: "Brazil",
    name_cn: "巴西",
    flag: "🇧🇷",
    currency: "BRL",
    min_amount: 10,
    max_amount: 10000,
    fee_rate: 3.0
  }
];

export const mockPaymentMethods = {
  ID: [
    {
      code: "202",
      name: "OVO Wallet",
      name_cn: "OVO钱包",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "203",
      name: "QRIS Scan",
      name_cn: "QRIS扫码",
      type: "scan",
      description: "QR code payment"
    },
    {
      code: "200",
      name: "Online Banking",
      name_cn: "网银支付",
      type: "online",
      description: "Bank transfer"
    },
    {
      code: "204",
      name: "DANA Wallet",
      name_cn: "DANA钱包",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "205",
      name: "GoPay",
      name_cn: "GoPay钱包",
      type: "wallet",
      description: "Digital wallet payment"
    }
  ],
  IN: [
    {
      code: "300",
      name: "Paytm",
      name_cn: "Paytm",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "301",
      name: "PhonePe",
      name_cn: "PhonePe",
      type: "wallet",
      description: "UPI payment"
    },
    {
      code: "302",
      name: "Google Pay",
      name_cn: "Google Pay",
      type: "wallet",
      description: "UPI payment"
    },
    {
      code: "303",
      name: "UPI Transfer",
      name_cn: "UPI转账",
      type: "upi",
      description: "Direct UPI transfer"
    }
  ],
  TH: [
    {
      code: "400",
      name: "TrueMoney",
      name_cn: "TrueMoney",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "401",
      name: "PromptPay",
      name_cn: "PromptPay",
      type: "scan",
      description: "QR code payment"
    },
    {
      code: "402",
      name: "Bank Transfer",
      name_cn: "银行转账",
      type: "bank",
      description: "Direct bank transfer"
    }
  ],
  VN: [
    {
      code: "500",
      name: "MoMo",
      name_cn: "MoMo",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "501",
      name: "ZaloPay",
      name_cn: "ZaloPay",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "502",
      name: "Bank Transfer",
      name_cn: "银行转账",
      type: "bank",
      description: "Direct bank transfer"
    }
  ],
  MY: [
    {
      code: "600",
      name: "GrabPay",
      name_cn: "GrabPay",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "601",
      name: "Boost",
      name_cn: "Boost",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "602",
      name: "Touch 'n Go",
      name_cn: "Touch 'n Go",
      type: "wallet",
      description: "Digital wallet payment"
    },
    {
      code: "603",
      name: "Online Banking",
      name_cn: "网银支付",
      type: "online",
      description: "Bank transfer"
    }
  ],
  BR: [
    {
      code: "700",
      name: "PIX",
      name_cn: "PIX",
      type: "scan",
      description: "Instant payment"
    },
    {
      code: "701",
      name: "Boleto",
      name_cn: "Boleto",
      type: "bank",
      description: "Bank slip payment"
    },
    {
      code: "702",
      name: "Credit Card",
      name_cn: "信用卡",
      type: "card",
      description: "Credit card payment"
    }
  ]
};

// 模拟API响应函数
export function mockGetCountries(lang = 'en') {
  return new Promise((resolve) => {
    setTimeout(() => {
      const countries = mockCountries.map(country => ({
        ...country,
        name: lang === 'cn' ? country.name_cn : country.name
      }));
      
      resolve({
        code: 1,
        msg: "Success",
        data: countries
      });
    }, 500);
  });
}

export function mockGetPayTypes(countryCode, lang = 'en') {
  return new Promise((resolve) => {
    setTimeout(() => {
      const methods = mockPaymentMethods[countryCode] || [];
      const localizedMethods = methods.map(method => ({
        ...method,
        name: lang === 'cn' ? method.name_cn : method.name
      }));
      
      resolve({
        code: 1,
        msg: "Success",
        data: localizedMethods
      });
    }, 300);
  });
}

export function mockCreateOrder(orderData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const orderNumber = 'WP' + Date.now() + Math.random().toString(36).substr(2, 5);
      
      resolve({
        code: 1,
        msg: "Order created successfully",
        data: {
          order_number: orderNumber,
          payment_url: `https://payment.example.com/pay/${orderNumber}`,
          amount: orderData.amount,
          currency: orderData.currency,
          status: 'pending'
        }
      });
    }, 800);
  });
}

export function mockGetOrderStatus(orderNumber) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 1,
        msg: "Success",
        data: {
          order_number: orderNumber,
          status: 'pending',
          amount: 1000,
          currency: 'IDR',
          country_code: 'ID',
          country_name: 'Indonesia',
          country_flag: '🇮🇩',
          payment_method_name: 'OVO Wallet',
          created_at: new Date().toISOString(),
          payment_info: {
            qr_code: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            instructions: '请使用OVO应用扫描二维码完成支付'
          }
        }
      });
    }, 400);
  });
}
