export default {
  line: "Ganti jalur",
  language: "<PERSON><PERSON>h bahasa",
  signin: "Daftar masuk",
  baozhengjin: "保证金",
  unbaozhengjin: "解冻保证金",
  common: [
    "Layanan online",
    "Batalkan penangguhan",
    "Lucky Draw",
    "Notifikasi",
    "Pengaturan",
    "Bantuan",
    "Tentang",
    "Konfirmasi"
  ],
  upload: [
    "Mengunggah...",
    "Kesalahan format",
    "Berhasil mengunggah",
    "Gagal mengunggah"
  ],
  footer: ["Halaman", "Tugas", "", "VIP", "Punya saya", "Pendapatan"],
  home: {
    broadcast:
      "Selamat untuk anggota{member}<br>Rekomendasi satu orang{vipname}<br>Mendapatkan{currency}{grade}Rekomendasi bonus!",
    taskHall: {
      title: ["Platform tugas", "Platform pelepasan pedagang"],
    },
    memberList: {
      title: "Daftar anggota",
      data: [
        "Selamat:{member}",
        "Hari ini selesai {num} pesanan, ambil hasil{currency}{profit}!"
      ],
    },
    businessList: {
      title: "Daftar pedagang",
      data: ["{member}", "Dirilis hari ini{num}Tugas tunggal"],
    },
    noticeTitle: "Catatan hangat",
    menu: ["Lokasi VIP", "Video tutorial", "Rekomendasi bonus"],
    msg: "Tugas tidak terbuka",
    video: "Saat ini tidak ada video tutorial",
    // Terjemahan baru halaman beranda
    notification: "Tingkatkan penjualan produk untuk mencapai profitabilitas",
    noMoreData: "Tidak ada data lagi",
    noTaskData: "Tidak ada data tugas",
    defaultTaskTitle: "Judul Tugas",
    commissionTag: "Komisi",
    logoText: "Cobwe",
    teamTab: "Tim",
    // Langkah proses
    processSteps: [
      "Bergabung Anggota",
      "Pilih Produk",
      "Pesan",
      "Dapatkan Komisi"
    ],
    // Tab kategori
    allCategory: "Semua",
    // Tombol urutkan
    priceSort: "Harga"
  },
  login: {
    text: [
      "Simpan informasi masuk",
      "Sedang masuk...",
      "Masuk sekarang",
      "Masuk"
    ],
    placeholder: ["Masukkan nomor ponsel atau email", "Masukkan kata sandi"],
    i18n: ["Tidak ada akun?{a} {line}", "Daftar"],
  },
  register: {
    text: [
      "Selamat datang untuk mendaftar",
      "Sedang mengirim kode sms...",
      "Mendapatkan pesan sms",
      "Sedang mendaftar...",
      "Daftar sekarang",
      "Sudah ada akun, segera unduh",
      "Setuju untuk melepaskan klausul",
      "Registrasi nomor ponsel",
      "Email registration",
      "Dapatkan kode email",
      "Mengirim kode e-mail..."
    ],
    placeholder: [
      "Masukkan nomor ponsel",
      "Masukkan kode verifikasi sms",
      "Masukkan kata sandi masuk",
      "Pastikan kata sandi Anda",
      "Masukkan kode undangan",
      "Kata sandi dua kali tidak sama",
      "Masukkan kode verifikasi",
      "Masukkan email",
      "Silakan periksa klausul pembatalan setuju",
      "Silakan masukkan kode verifikasi email"
    ],
    i18n: ["Sudah ada akun？{a} {line}", "Masuk"],
  },
  postTask: {
    navBar: {
      title: "Rilis tugas",
      right: "Aturan rilis",
    },
    field: [
      {
        label: "Jenis tugas",
      },
      {
        label: "Judul tugas",
        placeholder: "Masukkan judul tugas",
        error: "Judul tugas tidak boleh kosong",
      },
      {
        label: "Pengenalan tugas",
        placeholder: "Masukkan pengenalan tugas",
      },
      {
        label: "Harga satuan tugas",
        placeholder: "Masukkan harga unit tugas",
        right: "{currency}",
        error: [
          "Harga unit tugas tidak boleh kosong",
          "Harga unit tugas tidak boleh kurang dari1{currency}"
        ],
      },
      {
        label: "Jumlah yang di ambil",
        placeholder: "Masukkan jumlah yang di ambil",
        error: "Jumlah mengambil tugas tidak boleh kosong",
      },
      {
        label: "Jumlah berapa kali penarikan",
        placeholder: "Masukkan jumlah penarikan",
        right: "Kali/orang",
        error: "Jumlah penarikan tidak boleh kosong",
      },
      {
        label: "Jumlah harga tugas",
        error: "Total harga tugas belum di hitung",
      },
      {
        label: "Informasi tautan",
        placeholder: "Masukkan alamat tautan",
        error: "Informasi tautan tidak boleh kosong",
      },
      {
        label: "Level tugas",
      },
      {
        label: "Tanggal berakhir",
        placeholder: "Klik pilih waktu",
        error: "Batas waktu tidak boleh kosong",
      },
      {
        label: "Menyelesaikan persyaratan",
        error: "Pilih kondisi penyelesaian",
      },
      {
        label: "Permintaan pengiriman",
        placeholder: "Silakan masukkan permintaan muat naik",
      },
      {
        label: "Sampel ulasan",
      },
      {
        label: "Langkah pengoperasian",
        placeholder: "Langkah yang mudah dipahami untuk membantu menyelesaikan",
        error: "Langkah pengoperasian tidak boleh kosong",
        img: "Diagram langkah tidak sempurna",
      },
    ],
    button: "Kumpul",
    step: {
      title: "Langkah pengoperasian",
      left: "batalkan",
      right: "Selesai",
      placeholder: "Harap masukkan deskripsi langkah-langkah operasi",
      button: ["Hapus", "Tambah"],
    },
    tips: [
      "dari biaya platform {pump}{br}Rilis ini membutuhkan pembayaran sekitar {price}，pastikan saldo akun mencukupi {a}{br}Catatan: Platform ini melarang publikasi konten yang dilarang oleh hukum, seperti pengurangan poin, akun yang dilarang.",
      "Isi ulang",
      "Pertahankan setidaknya satu langkah",
      "Maksimal menambahkan 10 langkah"
    ],
  },
  vip: {
    user: {
      title: ["Identitas Anda", "Pengunjung"],
      label: "Tugas setiap hari",
      value: ["Tanggal berlaku", "Permanen", "Pilih masuk"],
    },
    list: {
      label: "Tugas setiap hari:{number}Kali",
      commission: "Setiap pesanan",
      button: ["Gabung sekarang", "Perbarui sekarang"],
      text: [
        "Pendapatan setiap hari",
        "Pendapatan setiap bulan",
        "Rekomendasi bonus : Setiap nama",
        "Gratis",
        "Setiap pesanan"
      ],
    },
    dialog: [
      "Yakin untuk membayar {amount} {currency} Menjadi{name}Apakah？",
      "Anda saat ini{currname}，Tidak dapat menjadi{name}",
      "Yakin untuk membayar {amount} {currency} Lanjut pembayaran{name}Apakah？"
    ],
    // VIP页面新增翻译
    expireTime: "Tanggal kedaluwarsa",
    benefitsTitle: "Hak istimewa eksklusif",
    loading: "Memuat...",
    loadingDesc: "Mendapatkan informasi tingkat anggota...",
    exclusivePrivilege: "Hak istimewa eksklusif {name}",
    upgradeCost: "Biaya upgrade",
    memberLevel: "Tingkat anggota",
    levelAuth: "Autentikasi anggota tingkat {level}",
    upgradeNow: "Upgrade sekarang untuk menikmati perlakuan VIP eksklusif!",
    referralReward: "Hadiah komisi rujukan",
    referralDesc: "Untuk setiap rujukan pengguna baru yang berhasil, Anda bisa mendapatkan",
    referralPercent: "{percent}% hadiah komisi rujukan",
    inviteMore: "Semakin banyak Anda mengundang, semakin kaya hadiahnya!",
    // Pesan status
    vipLocked: "Level VIP ini telah dikunci, tidak dapat dibeli",
    expired: "Kedaluwarsa",
    expireTime: "Waktu Kedaluwarsa",
    // Teks tombol
    buttons: {
      payNow: "Bayar sekarang",
      renewNow: "Perbarui sekarang",
      buyNow: "Beli sekarang"
    },
    // Detail manfaat
    benefits: {
      dailyTasks: "Batas tugas harian",
      dailyTasksDesc: "Dapat menyelesaikan <strong>{number} tugas</strong> per hari",
      taskRewards: "Nikmati lebih banyak peluang penghasilan tugas",
      earnMore: "Lebih banyak tugas, lebih banyak penghasilan!",
      lotteryTimes: "Kesempatan lotere harian",
      lotteryTimesDesc: "Dapatkan <strong>{times} kesempatan</strong> lotere per hari",
      lotteryChance: "Lebih banyak kesempatan, lebih banyak kemungkinan menang",
      lotteryRewards: "Roda keberuntungan, kejutan tak terbatas!",
      taskBonus: "Bonus pendapatan tugas",
      taskBonusDesc: "Selesaikan tugas untuk mendapatkan <strong>{income}x</strong> bonus pendapatan<br/>Buat setiap usaha yang Anda lakukan mendapatkan lebih banyak hadiah<br/>Dapatkan dengan efisien, kaya dengan mudah!",
      teamShare: "Berbagi pendapatan tim",
      teamShareDesc: "Nikmati <strong>{income1}x</strong> berbagi pendapatan dari anggota tim<br/>Semakin kuat tim, semakin kaya pendapatan Anda<br/>Pendapatan pasif, dapatkan saat tidur!"
    }
  },
  user: {
    default: [
      "Atasan saya",
      "Akun login",
      "Kode undangan",
      "Keluar",
      "Saldo",
      "Koin emas",
      "Dompet saya",
      "Diblokir",
      "Terbatas",
      "Baik",
      "Sangat baik"
    ],
    myEarnings: {
      grid: [
        "Saldo saya",
        "Pendapatan kemarin",
        "Pendapatan hari ini",
        "Pendapatan minggu ini",
        "Pendapatan bulan ini",
        "pendapatan bulan lalu",
        "Total pendapatan",
        "Tugas selesai hari ini",
        "Sisa tugas hari ini",
        "Komisi hari ini",
        "Rebate hari ini"
      ],
    },
    default: [
      "Level saya",
      "Masuk akun",
      "Kode undangan",
      "Keluar",
      "Saldo",
      "Koin",
      "Dompet saya",
      "Tutup akun",
      "Membatasi",
      "Baik",
      "Luar biasa"
    ],
    YUEBAO: [
      "Yuebao",
      "Metode Transfer Masuk",
      "Periode Penyimpanan",
      "hari",
      "Penghasilan per hari",
      "Jumlah Tersimpan",
      "Perkiraan pendapatan",
      "Silakan masukkan jumlah penyimpanan",
      "menentukan",
      "Silakan pilih informasi",
      "Silahkan pilih produknya dulu",
      "luar biasa",
      "robot",
      "Yuebao"
    ],
    newlist: [
      "Tidak ada catatan",
      "Perkiraan pendapatan",
      "Jumlah",
      "Waktu Pembelian",
      "status"
    ],
    menu: [
      "Catatan tugas",
      "Ulasan tugas",
      "Manajemen rilis",
      "Informasi pribadi",
      "Akun terikat",
      "Laporan harian",
      "Catatan perubahan akun",
      "Undang teman",
      "Laporan tim",
      "Bantuan manual",
      "Pusat kredit",
      "Unduh App",
      "Pembayaran VIP pihak ketiga"
    ],
    robot: [
      "Pembantu rumah awan",
      "Deskripsi layanan",
      "1. Gaji layanan pembukaan adalah 99 / bulan, efektif hari berikutnya",
      "2. Setelah itu berakhir, akan secara otomatis menyelesaikan tugas harian untuk pelanggan, dan secara otomatis menyelesaikan tugas harian dan menetapkan pendapatan sebelum 8 pagi setiap hari",
      "Pembantu rumah Awan Buka"
    ],
    // Terjemahan banner undangan
    inviteBanner: {
      title: "Undang untuk Mendapatkan Koin",
      subtitle: "Klik untuk Mengundang Teman"
    },
    YUEBAO: ["Yuebao", "Metode Transfer Masuk", "Periode Penyimpanan", "hari", "Penghasilan per hari", "Jumlah Tersimpan", "Perkiraan pendapatan", "Silakan masukkan jumlah penyimpanan", "menentukan", "Silakan pilih informasi", "Silahkan pilih produknya dulu", "luar biasa", "robot", "Yuebao"],
    newlist: ["Tidak ada catatan", "Perkiraan pendapatan", "Jumlah", "Waktu Pembelian", "status"],
    // Terjemahan khusus halaman saya
    myPage: {
      account: "Akun",
      inviteCode: "Kode Undangan",
      logout: "Keluar",
      balance: "Saldo",
      myWallet: "Dompet Saya",
      totalEarnings: "Total Pendapatan",
      todayRemaining: "Pendapatan Hari Ini",
      luckyDraw: "Lucky Draw",
      inviteFriends: "Undang Teman"
    }
  },
  userInfo: {
    default: [
      "Informasi pribadi",
      "Profil",
      "Nomor ponsel",
      "Kartu bank",
      "Alipay",
      "Informasi detail",
      "Kata sandi masuk",
      "Kata sandi dana",
      "Klik pengaturan",
      "Ubah profil",
      "Ubah kata sandi masuk",
      "Ubah kata sandi dana",
      "Kumpul",
      "Mengosongkan cache"
    ],
    label: [
      "Kata sandi masuk sebelumnya",
      "Kata sandi masuk baru",
      "Pastikan kata sandi",
      "Kata sandi semula",
      "Kata sandi baru dana",
      "Pastikan kata sandi"
    ],
    placeholder: [
      "Masukkan kata sandi sebelumnya",
      "Masukkan kata sandi baru",
      "Pastikan kata sandi masuk",
      "Masukkan kata sandi semula",
      "Masukkan kata sandi baru dana",
      "Pastikan kata sandi dana"
    ],
  },
  bankCard: {
    default: [
      "Kartu bank terikat",
      "Verifikasi sekarang",
      "Sedang mengumpul...",
      "Tambahkan sekarang",
      "Tambah kartu bank"
    ],
    tips: [
      "Verifikasi nama kemudian tambahkan kartu bank",
      "Nama pembukaan rekening dari kartu bank terikat Anda harus sama dengan nama asli terverifikasi Anda, jika tidak penarikan akan gagal."
    ],
    label: [
      "Nama",
      "Nama bank",
      "Akun bank",
      "Jenis bank",
      "Kode IFSC",
      "nomor telepon",
      "kotak surat",
      "Alamat dompet"
    ],
    placeholder: [
      "Pilih nama bank",
      "Masukkan akun bank",
      "Pilih jenis bank",
      "Masukkan kode IFSC",
      "Masukkan nomor telepon",
      "Masukkan email",
      "Masukkan alamat dompet"
    ],
  },
  userSet: {
    default: ["Verifikasi nama", "Detail informasi", "绑定支付宝", "Kumpul"],
    label: ["Nama asli", "QQ号", "支付宝账号", "支付宝姓名"],
    placeholder: [
      "Masukkan nama asli (Digunakan denga penarikan nama)",
      "请输入QQ号",
      "请输入支付宝账号",
      "请输入支付宝姓名"
    ],
    tips:
      "Kiat : Tidak dapat mengubah Bank terikat, gunakan untuk penarikan Anda",
  },
  bindAccount: {
    default: ["Akun terikat", "Lihat tutorial", "Kumpul"],
    tabs: ["Akun", "Akun", "Akun"],
    label: ["Panduan pengoperasian", "Tambah Scranshoot", "Akun"],
    placeholder: "Silahkan masukkan{account}Akun",
  },
  dayReport: [
    "Laporan harian",
    "Total pemasukan",
    "Tugas yang saya selesaikan",
    "Pendapatan tugas saya",
    "Tugas diselesaikan oleh bawahan",
    "Pendapatan tugas bawahan",
    "Pesanan",
    "30 hsri akhir ini",
    "Kuantitas",
    "Tugas",
    "Bawahan",
    "Biaya",
    "Tanggal"
  ],
  fundRecord: {
    default: [
      "Catatan pengeluaran",
      "Catatan isi ulang",
      "Catatan pendapatan",
      "Isi",
      "Pendapatan",
      "Bayar"
    ],
    tabs: ["Pendapatan", "Pengeluaran", "Isi ulang"],
    tradeDescription: "Deskripsi Transaksi"
  },
  vanPull: ["Tidak ada lebih banyak data lagi", "Tidak ada data"],
  promote: [
    "Teman Anda",
    "Mengundang Anda untuk bergabung{title}",
    "Kode rekomendasi",
    "Salin kode rekomendasi",
    "Salin tautan undangan",
    "Simpan kode QR",
    "Rekomendasi bonus",
    "Sukses menyimpan poster",
    "Gagal menyimpan poster, silahkan coba beberapa kali atau simpan scranshoot",
    "Silahkan scranshoot",
    "Simpan poster promosi",
    "Simpan poster promosi ke ponsel<br>Jika tidak berhasil silahkan coba beberapa kali atau simpan scranshoot"
  ],
  // Internasionalisasi halaman undangan
  invite: {
    inviteFriends: "Undang Teman",
    yourInviteCode: "Kode Undangan Anda",
    copyInviteCode: "Salin Kode Undangan",
    inviteLink: "Tautan Undangan",
    copyLink: "Salin Tautan",
    share: "Bagikan",
    shareInviteNow: "Bagikan Undangan Sekarang",
    qrCode: "Kode QR",
    saveSuccess: "Berhasil Disimpan",
    saveFailed: "Gagal Disimpan, Silakan Coba Lagi",
    copySuccess: "Berhasil Disalin",
    copyFailed: "Gagal Disalin",
    shareTitle: "Undang Teman untuk Mendapatkan Koin",
    shareDescription: "Bergabunglah dengan kami dan dapatkan hadiah besar bersama!",
    inviteRewards: "Hadiah Undangan",
    howToInvite: "Cara Mengundang",
    inviteSteps: {
      step1: "Bagikan kode atau tautan undangan dengan teman",
      step2: "Teman mendaftar dan menyelesaikan tugas pertama",
      step3: "Anda mendapat hadiah rujukan yang besar"
    }
  },
  teamReport: {
    default: [
      "Cari",
      "Pendapatan Rujukan",
      "Komisi Tugas",
      "Isi ulang tim",
      "Penarikan tim",
      "Jumlah tagihan",
      "Jumlah rekomendasi",
      "Jumlah tim",
      "Jumlah penambahan",
      "Jumlah",
      "Saya",
      "Pengguna",
      "Isi ulang",
      "Penarikan",
      "Potongan harga",
      "Komisi",
      "Pilih waktu",
      "Tidak ada data",
      "Jumlah isi ulang",
      "Jumlah penambahan orang",
      "Isi ulang rabat"
    ],
    tabs: ["Laporan tim", "Tim saya"],
    team: ["Bawahan Level 1", "Bawahan Level 2", "Bawahan Level 3"],
    structure: "Struktur Tim",
    details: "Detail Bawahan",
    loading: "Memuat data tim",
    noData: "Tidak ada data tim",
    userDefault: "Pengguna",
    groupId: "ID Grup",
    commission: "Komisi",
    unknown: "Tidak Diketahui",
    // Detail anggota popup
    memberDetail: "Detail Anggota",
    basicInfo: "Informasi Dasar",
    earningsInfo: "Informasi Pendapatan",
    teamInfo: "Informasi Tim",
    statusInfo: "Informasi Status",
    username: "Nama Pengguna",
    userId: "ID Pengguna",
    phone: "Telepon",
    email: "Email",
    regTime: "Waktu Registrasi",
    invitor: "Pengundang",
    totalCommission: "Total Komisi",
    todayCommission: "Komisi Hari Ini",
    totalRecharge: "Total Isi Ulang",
    balance: "Saldo",
    directCount: "Rujukan Langsung",
    teamCount: "Ukuran Tim",
    status: "Status",
    vipLevel: "Level VIP",
    lastLogin: "Login Terakhir",
    statusActive: "Aktif",
    statusInactive: "Tidak Aktif",
    statusSuspended: "Ditangguhkan",
    statusBanned: "Dilarang",
    statusUnknown: "Status Tidak Diketahui"
  },
  help: ["Bantu ponsel", "Tidak ada konten"],
  credit: [
    "Pusat kredit",
    "Panduan kredit",
    "Catatan kredit",
    "<p>1.Skor kredit dievaluasi setiap minggu</p><p>2.Skor kredit pengguna awal:<b>60</b></p><p>3.Jika terdeteksi bahwa pengguna melakukan tugas untuk mengunggah gambar palsu, itu akan dikurangi satu hari:<b>1</b>integral,Pengurangan maksimum:<b>7</b>integral</p><p>4.Jika tidak terdeteksi bahwa pengguna menggunakan gambar palsu untuk meningkatkan<b>1</b>integral</p><p>5.Skor kredit lebih rendah dari<b>50</b>Poin akan dibatasi penarikannya</p><p>6.Skor kredit lebih rendah dari<b>30</b>Membagi dua jumlah tugas</p><p>7.Skor kredit kurang dari atau sama<b>0</b>Poin akan dilarang</p>",
    "Kredit saya",
    "Tidak ada catatan kredit saat ini"
  ],
  upload: [
    "Mengunggah...",
    "Kesalahan format",
    "Berhasil mengunggah",
    "Gagal mengunggah"
  ],
  task: {
    default: [
      "Datar tugas",
      "Permintaan tugas",
      "Membuat",
      "Tinjau",
      "Buka tautan",
      "Salin tautan",
      "Patokan harga",
      "Kumpul",
      "Lepas"
    ],
    tabs: [
      "Pengolahan",
      "Sedang di tinjau",
      "Selesai",
      "Gagal",
      "Sengaja",
      "Sudah di lepas"
    ],
    msg: "Unggah gambar tugas yang sudah selesai",
    info: [
      "Detail tugas",
      "Judul tugas",
      "Pendapatan tugas",
      "Detail misi",
      "Unggah undangan",
      "Kumpul sampel",
      "Pengguna tidak mengirimkan sampel",
      "Tinjau instruksi",
      "Tanggal ulasan",
      "Sisi permintaan",
      "Merilis",
      "Salin",
      "Lewati",
      "Langkah tugas",
      "Ke{index}Tahap",
      "Sampel audit",
      "Tidak ada sampel ulasan",
      "Memuat...",
      "Lepas tugas",
      "Kumpul tugas selesai",
      "Rilis konten",
      "Konten yang dipublikasikan"
    ],
    index: [
      "Identitas saat inj",
      'Level Anda saat ini <i style="color:#1989fa">{currVip}</i><br>Hanya dapat mengambil saat ini<i style="color:#1989fa">{currVip}</i>Tugas level<br>Apakah akan gabung <i style="color:#dd6161">{vip}</i> Level',
      "Gabung sekarang",
      "Pilih tipe tugas"
    ],
    list: ["Daftar tugas", "Sisi permintaan", "Sisa", "Permintaan", "Ambil"],
    show: [
      "Detail tugas",
      "Orang sudah menghasilkan uang",
      "Sisa{num}tempat tersisa",
      "Tinjau dalam 48 jam",
      "Deskripsi tugas",
      "Sisi permintaan",
      "Standar audit",
      "Salin",
      "Lewati",
      "Langkah-langkah tugas",
      "Ke{index}Tahap",
      "Sampel audit",
      "Tidak ada sampel ulasan",
      "Memuat...",
      "Sedang mengumpul...",
      "Ambil tugas",
      "Masuk sekarang",
      "Rilis konten",
      "Detail Produk",
      "Gambar Produk",
      "Judul Produk",
      "Cashback",
      "Harga",
      "Terjual",
      "Beli Sekarang",
      "Konfirmasi Pembelian",
      "Konfirmasi Beli",
      "Batal",
      "Apakah Anda yakin ingin membeli tugas ini?",
      "Jumlah yang sesuai akan dipotong setelah pembelian",
      "Pembelian Berhasil",
      "Kembali ke Beranda",
      "Tugas berhasil dibeli!",
      "Anda dapat memeriksa kemajuan di Tugas Saya",
      "Harga Beli:",
      "Komisi Diperoleh:",
      "Persyaratan Level",
      "Level Tidak Cukup",
      "Memuat informasi tugas",
      "Tugas sudah diambil",
      "Tugas sudah penuh",
      "Sementara tidak dapat dibeli",
      "Tugas ini sudah diambil, silakan pilih tugas lain",
      "Tugas ini sudah penuh, silakan perhatikan tugas lain",
      "Silakan coba lagi nanti atau hubungi layanan pelanggan",
      "Klik untuk detail",
      "Level tidak cocok, tidak dapat membeli",
      "Tugas ini memerlukan level {requiredLevel}, level Anda saat ini tidak cocok",
      "Tugas ini memerlukan level {requiredLevel}, saat ini Anda adalah {currentLevel}, level tidak cocok tidak dapat membeli",
      "Keanggotaan Kedaluwarsa",
      "Keanggotaan Anda telah kedaluwarsa, silakan perpanjang sebelum membeli tugas",
      "Keanggotaan Anda telah kedaluwarsa, silakan perpanjang sebelum membeli tugas",
      "Perpanjang Sekarang"
    ],
  },
  serviceCenter: [
    "Pusat layanan pelanggan",
    "Hi, Layanan pelanggan yang berdedikasi",
    "Sangat senang telah memberikan pelayanan kepada Anda",
    "Layanan mandiri",
    "Layanan online",
    "Layanan isi ulang",
    "Layanan Line"
  ],
  audit: {
    default: [
      "Tugas audit",
      "Ambil pengguna",
      "Ambil tanggal",
      "Perbaharui tanggal",
      "Ulasan"
    ],
    tabs: ["Pengolahan", "Sedang mengulas", "Sudah selesai", "Gagal"],
    info: [
      "Detail ulasan",
      "Judul tugas",
      "Tital tugas",
      "Orang yang sudah menyelesaikan",
      "Sisa{num}Sisa nama",
      "Detail misi",
      "Pesan tautan",
      "Sampel ulasan",
      "Ambil pengguna",
      "Ambil/mengambil",
      "Kondisi selesai",
      "Kirim ulasan",
      "Pengguna tidak mengirimkan sampel",
      "Perbaharui tanggal",
      "Memuat...",
      "Sengaja",
      "Ulasan",
      "Gagal",
      "Sukses",
      "Ulasan tugas",
      "Tinjauan instruksi",
      "Silakan masukkan tinjauan instruksi",
      "Tugas yang dikirim tidak memenuhi syarat, perlu mengirim ulang ulasan",
      "Selamat tugas sudah selesai, semangat lagi",
      "Scranshoot salah dari halaman tugas yang dikirimkan, tugas gagal",
      "Sengaja mengumpulkan tugas, tugas gagal"
    ],
  },
  postRecord: [
    "Manajemen rilis",
    "Rilis",
    "Total",
    "Sudah selesai",
    "Batas waktu",
    "Ulasan",
    "Mencabut",
    "Ubah"
  ],
  wallet: {
    default: [
      "Dompet sata",
      "Isi ulang",
      "Penarikan",
      "Catatan isi ulang",
      "Catatan penarikan",
      "Isi ulang",
      "Mengambil",
      "Alipay"
    ],
    label: [
      "Metode penarikan",
      "Jumlah penarikan",
      "Kata sandi dana",
      "Kumpul",
      "Nomor ponsel",
      "Email",
      "IFSC",
      "Bank penarikan",
      "Biaya penarikan"
    ],
    placeholder: [
      "Pilih metode penarikan",
      "Masukkan jumlah penarikan",
      "Masukkan kata sandi dana",
      "Silakan pilih metode penarikan",
      "Masukkan nomor ponsel penerima",
      "Masukkan email penerima",
      "IFSC Masukkan IFSC penerima",
      "Silakan pilih bank penarikan"
    ],
    msg: [
      "Anda belum mengatur kata sandi dana, silakan atur terlebih dahulu",
      "Anda belum mengikat rekening, silakan ikat terlebih dahulu"
    ],
    info: {
      currentBalance: "Saldo Saat Ini",
      withdrawAmount: "Jumlah Penarikan",
      fee: "Biaya",
      totalDeduction: "Total Potongan"
    },
    warning: {
      insufficientFunds: "Saldo Tidak Cukup",
      shortfall: "Kekurangan",
      insufficientBalance: "Saldo tidak cukup! Jumlah penarikan {currency}{withdrawAmount}, saldo saat ini {currency}{currentBalance}, kekurangan {currency}{shortfall}",
      insufficientBalanceWithFee: "Saldo tidak cukup! Jumlah penarikan {currency}{withdrawAmount} + biaya {currency}{fee} = total {currency}{totalRequired}, saldo saat ini {currency}{currentBalance}, kekurangan {currency}{shortfall}"
    },
  },
  recharge: {
    default: [
      "Isi ulang",
      "Detail isi ulang",
      "Catatan isi ulang",
      "Sisa saldo{money}，Pilih metode isi ulang",
      "Jumlah minimum tunggal adalah{currency}{min}，Maksimal{currency}{max}，Biaya admin{fee}%",
      "Sedang mengumpul...",
      "Isi ulang sekarang",
      "Kembali",
      "Memuat..."
    ],
    info: [
      "Jumlah isi ulang",
      "Nomor pesanan",
      "Bank penerima",
      "Rekening penerima",
      "Penerima",
      "Nama pembayar",
      "Nomor telepon pembayar",
      "Akun UPI pembayar",
      "Email pembayar",
      "Nama bank",
      "Jenis akun",
      "Keterangan"
    ],
    placeholder: [
      "Masukkan jumlah isi ulang",
      "Pilih saluran isi ulang",
      "Masukkan nama transfer",
      "Jumlah minimum tunggal adalah {currency}{min}",
      "Jumlah maksimum tunggal adalah {currency}{max}",
      "Masukkan nama pembayar",
      "Masukkan nomor telepon pembayar, tambahkan kode internasional, mis. 86",
      "Masukkan akun UPI pembayar",
      "Masukkan email pembayar"
    ],
    label: [
      "Jumlah isi ulang",
      "Saluran isi ulang",
      "Nama transfer",
      "Nama pembayar",
      "Nomor telepon pembayar",
      "Akun UPI pembayar",
      "Email pembayar"
    ],
    tips: [
      "Silakan pilih metode berikut untuk mentransfer jumlah yang sesuai untuk menghindari keterlambatan pengumpulan keuangan<br>Setelah transfer, harap unggah tangkapan layar transfer sebagai bukti verifikasi",
      "Tidak perlu menambah teman, pindai kode QR untuk mentransfer uang kepada saya",
      "Harap selesaikan transfer sesuai dengan informasi di bawah ini",
      "Tips: Setelah pembayaran berhasil, harap hubungi layanan pelanggan online dan berikan akun anggota Anda, jumlah isi ulang, nomor pesanan, akun penyetor, waktu isi ulang; untuk memfasilitasi penambahan dana tepat waktu oleh keuangan",
      "Catatan: Anggota harap kirimkan setiap pembayaran transfer sekali",
      "Setelah transfer, harap unggah tangkapan layar transfer sebagai bukti verifikasi",
      "Harap pilih metode berikut untuk mentransfer jumlah yang sesuai",
      "Harap unggah tangkapan layar transfer"
    ]
  },
  dialog: [
    "Tip/kiat",
    "Yakin",
    "Sedang mengumpul...",
    "Berhasil menyalin",
    "Versi sistem IOS rendah tidak mendukung",
    "Mendaftarkan ...",
    "Memuat...",
    "Nilai penuh"
  ],
  lineList: ["Pemilihan rute", "Rute saat ini", "Rute"],
  newLc: [
    "Transfer dalam metode",
    "Balas rekening",
    "Periode penyimpanan",
    "hari",
    "Daftar per hari",
    "Jumlah deposit",
    "Silakan masukkan jumlah deposit",
    "keuntungan",
    "Kirim",
    "Finansi balans",
    "Silakan isi informasi",
    "Silakan pilih produk pertama",
    "rekaman"
  ],
  newLcList: [
    "Catatan pembelian",
    "Tidak ada catatan",
    "keuntungan",
    "jumlah uang",
    "Waktu pembelian",
    "keadaan"
  ],
  wheel: [
    "Selamat telah memenangkan hadiahnya {name},Sistem telah membagikan hadiah",
    "Selamat telah memenangkan hadiahnya {name}, Silakan hubungi layanan pelanggan untuk menerima penghargaan",
    "Pertahankan kerja bagus dan Anda akan menang di lain waktu",
    "Deskripsi undian berhadiah",
    "Undian",
    "Meja putar besar",
    "Sisa kesempatan undian: ",
    "Mulai",
    "Sisa kesempatan undian tidak cukup"
  ],
  appMsg: [
    "Bersiap mengunduh pembaruan...",
    "Unduhan selesai. Apakah akan menginstal paket pembaruan?",
    "Gagal mengunduh pembaruan",
    "{num}% terunduh",
    "Memperbarui...",
    "Pembaruan berhasil. Akan restart segera",
    "Pembaruan gagal",
    "Tekan lagi untuk keluar dari aplikasi"
  ],
  appDown: [
    "Unduh Aplikasi",
    "Pindai kode QR untuk mengunduh aplikasi",
    "Simpan kode QR",
    "Kode QR berhasil disimpan",
    "Gagal menyimpan kode QR, silakan coba beberapa kali atau simpan screenshot"
  ],
  buyVip: [
    "Agen untuk membeli VIP",
    "Akun isi ulang",
    "Silakan masukkan nomor akun isi ulang",
    "Isi ulang VIP",
    "Silakan pilih isi ulang VIP",
    "Apakah Anda yakin untuk mengisi ulang {grade} untuk anggota {user}?",
    "Kirim"
  ],
  Activity: [
    "Like Share Karnaval Jutaan Dolar",
    "Halaman sedang dimuat..."
  ],
  usdt: ["Alamat bank", "Ikat alamat bank", "Tambah alamat bank"],
  wheelRecords: {
    title: "Catatan Kemenangan",
    unknownPrize: "Hadiah Tidak Dikenal",
    times: " kali",
    prizeTypes: {
      task: "Hadiah Tugas",
      money: "Hadiah Koin",
      other: "Hadiah Lainnya",
      thankYou: "Terima Kasih Telah Berpartisipasi"
    },
    status: {
      received: "Diterima",
      pending: "Menunggu",
      unknown: "Tidak Dikenal",
      expired: "Kedaluwarsa",
      valid: "Valid",
      processing: "Sedang Diproses"
    },
    timeFormat: {
      justNow: "Baru saja",
      minutesAgo: " menit lalu",
      hoursAgo: " jam lalu",
      daysAgo: " hari lalu"
    },
    errors: {
      timeFormatError: "Kesalahan format waktu:"
    }
  },

  // Pembayaran Global Multi-bahasa
  globalPay: {
    title: "Pembayaran Global",
    description: "Pilih metode pembayaran Anda untuk top up yang aman dan mudah",
    selectCountry: "Pilih Negara",
    selectCountryPlaceholder: "Silakan pilih negara",
    selectCountrySubtitle: "Pilih negara atau wilayah Anda",
    selectPaymentMethod: "Pilih Metode Pembayaran",
    selectPaymentSubtitle: "Pilih metode pembayaran yang Anda sukai",
    paymentAmount: "Jumlah Pembayaran",
    enterAmountSubtitle: "Masukkan jumlah yang ingin Anda top up",
    orderNumber: "Nomor Pesanan",
    paymentStatus: "Status Pembayaran",

    steps: {
      selectCountry: "Pilih Negara",
      selectPayment: "Pilih Pembayaran",
      enterAmount: "Masukkan Jumlah"
    },

    quickAmounts: "Jumlah Cepat",
    amountRange: "Rentang Jumlah",
    orderSummary: "Ringkasan Pesanan",
    country: "Negara",
    paymentMethod: "Metode Pembayaran",
    amount: "Jumlah",
    fee: "Biaya",
    feeRate: "Tarif Biaya",
    total: "Total",

    tabs: {
      select: "Pilih Pembayaran",
      processing: "Memproses",
      completed: "Selesai"
    },

    countries: {
      ID: "Indonesia",
      IN: "India",
      TH: "Thailand",
      VN: "Vietnam",
      MY: "Malaysia",
      BR: "Brasil",
      CN: "Tiongkok",
      TW: "Taiwan",
      HK: "Hong Kong",
      PH: "Filipina",
      SG: "Singapura"
    },

    paymentMethods: {
      wallet: "Dompet Digital",
      bank: "Transfer Bank",
      card: "Kartu Bank",
      scan: "Pembayaran QR",
      online: "Internet Banking",
      upi: "Pembayaran UPI",
      qris: "Pembayaran QRIS",
      ovo: "Dompet OVO",
      dana: "Dompet DANA",
      gopay: "Dompet GoPay",
      shopeepay: "ShopeePay",
      linkaja: "LinkAja",
      paytm: "Paytm",
      phonepe: "PhonePe",
      googlepay: "Google Pay",
      truemoney: "TrueMoney",
      promptpay: "PromptPay",
      momo: "MoMo",
      zalopay: "ZaloPay",
      grabpay: "GrabPay",
      boost: "Boost",
      tng: "Touch 'n Go",
      pix: "PIX",
      boleto: "Boleto"
    },

    status: {
      pending: "Menunggu",
      processing: "Memproses",
      completed: "Selesai",
      failed: "Pembayaran Gagal",
      cancelled: "Dibatalkan",
      expired: "Kedaluwarsa"
    },

    messages: {
      loading: "Memuat...",
      selectCountryFirst: "Silakan pilih negara terlebih dahulu",
      selectPaymentMethodFirst: "Silakan pilih metode pembayaran terlebih dahulu",
      enterAmount: "Silakan masukkan jumlah pembayaran",
      noPaymentMethods: "Tidak ada metode pembayaran yang tersedia untuk negara ini",
      processing: "Memproses...",
      amountTooLow: "Jumlah pembayaran tidak boleh kurang dari {min}",
      amountTooHigh: "Jumlah pembayaran tidak boleh melebihi {max}",
      orderCreated: "Pesanan berhasil dibuat",
      paymentSuccess: "Pembayaran berhasil",
      paymentFailed: "Pembayaran gagal",
      networkError: "Kesalahan jaringan, silakan coba lagi",
      redirecting: "Mengarahkan ke halaman pembayaran..."
    },

    buttons: {
      payNow: "Bayar Sekarang",
      cancel: "Batal",
      retry: "Coba Lagi",
      back: "Kembali",
      confirm: "Konfirmasi",
      selectCountry: "Pilih Negara",
      selectPayment: "Pilih Metode Pembayaran"
    },

    selectBank: "Pilih Bank",

    placeholders: {
      searchCountry: "Cari Negara",
      enterAmount: "Masukkan Jumlah"
    },

    tips: {
      selectCountryTip: "Silakan pilih negara Anda untuk menampilkan metode pembayaran yang tersedia",
      paymentMethodTip: "Pilih metode pembayaran yang paling sesuai untuk Anda",
      amountTip: "Minimum {min}, Maksimum {max} per transaksi",
      processingTip: "Pembayaran sedang diproses, jangan tutup halaman",
      successTip: "Pembayaran berhasil, dana akan masuk dalam 5-10 menit"
    }
  }
};
