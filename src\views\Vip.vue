<template>
  <div class="Site PageBox no-padding">
    <div class="ScrollBox">
      <div class="vip-page">
        <!-- VIP卡片轮播 -->
        <div class="vip-cards-container">
          <!-- 左箭头 -->
          <div class="swipe-arrow swipe-arrow-left" @click="prevCard" v-if="gradeList.length > 1">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M15 18L9 12L15 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>

          <!-- 右箭头 -->
          <div class="swipe-arrow swipe-arrow-right" @click="nextCard" v-if="gradeList.length > 1">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M9 18L15 12L9 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>

      <van-swipe
        :autoplay="0"
        :show-indicators="false"
        :initial-swipe="currentCardIndex"
        class="vip-swipe"
        @change="onCardChange"
        ref="vipSwipe"
      >
        <!-- 动态生成VIP卡片 -->
        <van-swipe-item v-for="(grade, index) in gradeList" :key="grade.grade">
          <div class="vip-card">
            <div class="card-background" :class="getVipCardClass(grade.grade)">
              <div class="card-content">
                <div class="agent-title">{{ grade.name }}</div>
              </div>
              <!-- 显示当前用户VIP到期时间或已过期状态 - 放在卡片中间 -->
              <div v-if="UserInfo && UserInfo.vip_level === grade.grade" class="vip-expire-center">
                <span v-if="isVipTimeValid()">
                  {{ $t('vip.expireTime') }}: {{ formatDate(UserInfo.etime) }}
                </span>
                <span v-else class="vip-expired">
                  {{ $t('vip.expired') }}
                </span>
              </div>
              <div class="card-footer">
                <div class="price-tag">
                  {{ $Currency.formatAmount(grade.amount) }}
                </div>
              </div>
            </div>
          </div>
        </van-swipe-item>


      </van-swipe>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 立即付费按钮 -->
      <div class="exclusive-button-container">
        <button
          class="exclusive-button"
          :class="{ 'button-disabled': isVipLocked }"
          @click="submitBuy(currentGradeInfo && currentGradeInfo.amount, currentGradeInfo && currentGradeInfo.grade, currentGradeInfo && currentGradeInfo.name)"
          :disabled="isVipLocked"
          v-if="shouldShowButton"
        >
          {{ getButtonText() }}
        </button>
      </div>

      <!-- 优势列表 -->
      <div class="benefits-container">
        <div class="benefits-title">{{ $t('vip.benefitsTitle') }}</div>

        <!-- 显示特权信息：优先使用后台数据，否则显示默认内容 -->
        <div v-if="currentGradeInfo" class="privilege-content">
          <!-- 如果有后台返回的特权描述，过滤不换行样式后显示 -->
          <div v-if="currentGradeInfo.privilege_description" v-html="getFilteredPrivilegeDescription()"></div>

          <!-- 如果没有后台数据，显示默认特权内容 -->
          <div v-else class="default-privilege">
            <div class="benefit-item">
              <div class="benefit-number">1.</div>
              <div class="benefit-content">
                <div class="benefit-title">
                  <span class="benefit-icon">🎯</span>
                  {{ $t('vip.exclusivePrivilege', { name: currentGradeInfo.name }) }}
                </div>
                <div class="benefit-description">
                  <strong>{{ $t('vip.upgradeCost') }}：</strong>{{ $Currency.formatAmount(currentGradeInfo.amount) }}<br/>
                  <strong>{{ $t('vip.memberLevel') }}：</strong>{{ $t('vip.levelAuth', { level: currentGradeInfo.grade }) }}<br/>
                  {{ $t('vip.upgradeNow') }}
                </div>
              </div>
            </div>

            <!-- 每日任务次数特权 -->
            <div v-if="currentGradeInfo.number !== undefined" class="benefit-item">
              <div class="benefit-number">2.</div>
              <div class="benefit-content">
                <div class="benefit-title">
                  <span class="benefit-icon">📋</span>
                  {{ $t('vip.benefits.dailyTasks') }}
                </div>
                <div class="benefit-description">
                  <span v-html="$t('vip.benefits.dailyTasksDesc', { number: currentGradeInfo.number })"></span><br/>
                  {{ $t('vip.benefits.taskRewards') }}<br/>
                  {{ $t('vip.benefits.earnMore') }}
                </div>
              </div>
            </div>

            <!-- 每日抽奖次数特权 -->
            <div v-if="currentGradeInfo.daily_turntable_times !== undefined" class="benefit-item">
              <div class="benefit-number">{{ getLotteryBenefitNumber() }}.</div>
              <div class="benefit-content">
                <div class="benefit-title">
                  <span class="benefit-icon">🎰</span>
                  {{ $t('vip.benefits.lotteryTimes') }}
                </div>
                <div class="benefit-description">
                  <span v-html="$t('vip.benefits.lotteryTimesDesc', { times: currentGradeInfo.daily_turntable_times })"></span><br/>
                  {{ $t('vip.benefits.lotteryChance') }}<br/>
                  {{ $t('vip.benefits.lotteryRewards') }}
                </div>
              </div>
            </div>

            <!-- 推荐佣金奖励 -->
            <div v-if="currentGradeInfo.commission !== undefined" class="benefit-item">
              <div class="benefit-number">{{ getCommissionBenefitNumber() }}.</div>
              <div class="benefit-content">
                <div class="benefit-title">
                  <span class="benefit-icon">💰</span>
                  {{ $t('vip.referralReward') }}
                </div>
                <div class="benefit-description">
                  {{ $t('vip.referralDesc') }}<br/>
                  <strong>{{ $t('vip.referralPercent', { percent: currentGradeInfo.commission }) }}</strong><br/>
                  {{ $t('vip.inviteMore') }}
                </div>
              </div>
            </div>
          </div>
        </div>



        <!-- 如果没有数据时显示默认内容 -->
        <div v-if="!currentGradeInfo" class="benefit-item">
          <div class="benefit-number">1.</div>
          <div class="benefit-content">
            <div class="benefit-title">{{ $t('vip.loading') }}</div>
            <div class="benefit-description">
              {{ $t('vip.loadingDesc') }}
            </div>
          </div>
        </div>
      </div>
    </div>

        <!-- 底部导航 -->
        <Footer />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Vip",
  components: {},
  props: [],
  data() {
    return {
      currentCardIndex: 0,
    };
  },
  computed: {
    gradeList() {
      if (this.InitData && this.InitData.UserGradeList) {
        console.log('计算属性 gradeList:', this.InitData.UserGradeList);
        return this.InitData.UserGradeList;
      }
      return [];
    },
    // 当前显示的会员等级信息
    currentGradeInfo() {
      if (this.gradeList.length > 0) {
        // 根据当前卡片索引获取对应的会员等级信息
        return this.gradeList[this.currentCardIndex] || this.gradeList[0];
      }
      return null;
    },
    // 当前用户的VIP等级
    currentUserGrade() {
      return this.UserInfo && this.UserInfo.vip_level ? this.UserInfo.vip_level : 0;
    },
    // 检查当前选中的VIP等级是否被锁定
    isVipLocked() {
      return this.currentGradeInfo && this.currentGradeInfo.is_locked == 1;
    },
    // 是否显示按钮
    shouldShowButton() {
      if (!this.currentGradeInfo) return false;
      const selectedGrade = this.currentGradeInfo.grade;
      const userGrade = this.currentUserGrade;

      // 如果用户是普通会员（grade=1）且选中的也是普通会员，不显示按钮
      if (userGrade === 1 && selectedGrade === 1) {
        return false;
      }

      // 如果选中的等级等于用户当前等级（续费情况）
      if (selectedGrade === userGrade) {
        // 检查是否可以续费（到期前3天内或已到期）
        return this.isVipExpired();
      }

      // 如果选中的等级大于用户当前等级，显示购买按钮
      return selectedGrade > userGrade;
    }
  },
  watch: {},
  created() {
    this.$Model.GetUserInfo();
    if (!this.VipList) {
      this.$Model.GetBackData((data) => {
        console.log('VIP页面获取到的数据:', data);
        console.log('UserGradeList:', data.UserGradeList);
        this.setDefaultCardIndex();
      });
    } else {
      this.setDefaultCardIndex();
    }

    // 添加调试信息
    console.log('VIP页面初始化');
    console.log('InitData:', this.InitData);
    console.log('UserGradeList:', this.InitData.UserGradeList);
  },
  mounted() {
    this.setDefaultCardIndex();
  },
  activated() {
    // 每次激活页面时重新设置卡片索引，以支持从任务页面跳转
    this.setDefaultCardIndex();
  },
  destroyed() {
    this.$toast.clear();
  },
  methods: {
    // 过滤特权描述中的不换行样式
    getFilteredPrivilegeDescription() {
      if (!this.currentGradeInfo || !this.currentGradeInfo.privilege_description) {
        return '';
      }

      let content = this.currentGradeInfo.privilege_description;

      // 移除所有的 text-wrap-mode: nowrap 样式
      content = content.replace(/text-wrap-mode:\s*nowrap;?/gi, '');

      // 移除所有的 white-space: nowrap 样式
      content = content.replace(/white-space:\s*nowrap;?/gi, '');

      // 移除所有的 word-break: keep-all 样式
      content = content.replace(/word-break:\s*keep-all;?/gi, '');

      // 清理空的style属性
      content = content.replace(/style="\s*"/gi, '');
      content = content.replace(/style=''\s*/gi, '');

      return content;
    },

    // 获取按钮文本
    getButtonText() {
      if (!this.currentGradeInfo) return this.$t('vip.buttons.payNow');
      const selectedGrade = this.currentGradeInfo.grade;
      const userGrade = this.currentUserGrade;

      if (selectedGrade === userGrade) {
        // 只有在可以续费时才显示续费按钮（到期前3天内或已到期）
        if (this.isVipExpired()) {
          return this.$t('vip.buttons.renewNow');
        }
        return this.$t('vip.buttons.payNow'); // 这种情况下按钮不应该显示
      } else if (selectedGrade > userGrade) {
        return this.$t('vip.buttons.buyNow');
      }
      return this.$t('vip.buttons.payNow');
    },
    // 设置默认显示的卡片索引
    setDefaultCardIndex() {
      if (this.gradeList.length === 0) return;

      // 检查是否有目标等级参数（从任务页面跳转过来）
      const targetLevel = this.$route.query.targetLevel;
      if (targetLevel) {
        const targetLevelNum = parseInt(targetLevel);
        const targetIndex = this.gradeList.findIndex(grade => grade.grade === targetLevelNum);
        if (targetIndex !== -1) {
          this.currentCardIndex = targetIndex;
          console.log('设置卡片索引为目标等级:', targetIndex, '等级:', targetLevelNum);
          this.$nextTick(() => {
            if (this.$refs.vipSwipe) {
              this.$refs.vipSwipe.swipeTo(targetIndex);
            }
          });
          return;
        }
      }

      // 默认显示用户当前等级的卡片
      if (this.UserInfo && this.UserInfo.vip_level) {
        const userLevel = this.UserInfo.vip_level;
        const index = this.gradeList.findIndex(grade => grade.grade === userLevel);
        if (index !== -1) {
          this.currentCardIndex = index;
          console.log('设置默认卡片索引为用户当前等级:', index, '等级:', userLevel);
          this.$nextTick(() => {
            if (this.$refs.vipSwipe) {
              this.$refs.vipSwipe.swipeTo(index);
            }
          });
        }
      }
    },
    onCardChange(index) {
      console.log('卡片切换到索引:', index);
      this.currentCardIndex = index;
      console.log('当前显示的会员信息:', this.currentGradeInfo);
    },
    // 上一张卡片
    prevCard() {
      if (this.$refs.vipSwipe && this.gradeList.length > 1) {
        const prevIndex = this.currentCardIndex > 0 ? this.currentCardIndex - 1 : this.gradeList.length - 1;
        this.$refs.vipSwipe.swipeTo(prevIndex);
      }
    },
    // 下一张卡片
    nextCard() {
      if (this.$refs.vipSwipe && this.gradeList.length > 1) {
        const nextIndex = this.currentCardIndex < this.gradeList.length - 1 ? this.currentCardIndex + 1 : 0;
        this.$refs.vipSwipe.swipeTo(nextIndex);
      }
    },
    submitBuy(amount, grade, name) {
      if (!this.UserInfo) {
        this.$router.push("/login");
        return;
      }

      // 检查VIP等级是否被锁定
      if (this.isVipLocked) {
        this.$toast.fail(this.$t('vip.vipLocked') || '该VIP等级已被锁定，无法购买');
        return;
      }
      var html = this.$t("vip.dialog[0]", {
        currency: this.$Currency.getSymbol(),
        amount: amount,
        name: name,
      });
      if (this.UserInfo.vip_level > grade) {
        this.$toast.fail(
          this.$t("vip.dialog[1]", {
            currname: this.UserInfo.useridentity,
            name: name,
          })
        );
      } else {
        if (this.UserInfo.vip_level == grade) {
          html = this.$t("vip.dialog[2]", {
            currency: this.$Currency.getSymbol(),
            amount: amount,
            name: name,
          });
        }
        this.$Dialog.Confirm(html, () => {
          this.$Model.BuyVip({ grade: grade }, (data) => {
            if (data.code == 1) {
              this.$Model.GetUserInfo();
            }
            if (data.code == 2) {
              this.$router.push(
                "/user/watchPay?amount=" + Number(data.amount || amount)
              );
            }
          });
        });
      }
    },
    // 根据VIP等级获取对应的CSS类名
    getVipCardClass(grade) {
      // 确保grade是数字
      const vipLevel = parseInt(grade) || 1;
      // 限制在1-9范围内，超出范围使用默认图片
      const imageLevel = Math.min(Math.max(vipLevel, 1), 9);

      return `vip-level-${imageLevel}`;
    },
    // 格式化日期显示
    formatDate(dateString) {
      if (!dateString) return '';
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return dateString;
      }
    },
    // 检查VIP时间字段是否有效（stime和etime都不为空）
    isVipTimeValid() {
      if (!this.UserInfo) {
        return false;
      }

      // 检查stime和etime是否都存在且不为空
      const hasValidStime = this.UserInfo.stime && this.UserInfo.stime.trim() !== '';
      const hasValidEtime = this.UserInfo.etime && this.UserInfo.etime.trim() !== '';

      console.log('VIP时间有效性检查:', {
        stime: this.UserInfo.stime,
        etime: this.UserInfo.etime,
        hasValidStime: hasValidStime,
        hasValidEtime: hasValidEtime,
        isValid: hasValidStime && hasValidEtime
      });

      return hasValidStime && hasValidEtime;
    },

    // 检查VIP是否可以续费（到期前3天内或已到期）
    isVipExpired() {
      // 如果时间字段无效，显示为已过期，可以续费
      if (!this.isVipTimeValid()) {
        console.log('VIP续费检查: 时间字段无效，显示为已过期，可以续费');
        return true;
      }

      try {
        const expireDate = new Date(this.UserInfo.etime);
        const currentDate = new Date();

        // 计算到期前3天的时间点
        const threeDaysBeforeExpire = new Date(expireDate);
        threeDaysBeforeExpire.setDate(expireDate.getDate() - 3);

        // 如果当前时间在到期前3天内或已到期，则可以续费
        const canRenew = currentDate >= threeDaysBeforeExpire;

        console.log('VIP续费检查:', {
          expireDate: this.formatDate(this.UserInfo.etime),
          currentDate: this.formatDate(currentDate.toISOString()),
          threeDaysBeforeExpire: this.formatDate(threeDaysBeforeExpire.toISOString()),
          canRenew: canRenew
        });

        return canRenew;
      } catch (error) {
        console.error('VIP到期时间判断错误:', error);
        // 如果日期解析出错，为了安全起见，显示为已过期，可以续费
        return true;
      }
    },
    // 获取抽奖次数特权的编号
    getLotteryBenefitNumber() {
      if (!this.currentGradeInfo) return 3;
      let number = 2; // 基础特权是1，任务次数是2

      // 如果有每日任务次数特权，抽奖次数编号是3
      if (this.currentGradeInfo.number !== undefined) {
        number = 3;
      } else {
        // 如果没有任务次数特权，抽奖次数编号是2
        number = 2;
      }

      return number;
    },
    // 获取推荐佣金奖励的编号
    getCommissionBenefitNumber() {
      if (!this.currentGradeInfo) return 2;
      let number = 2; // 从第2个开始，第1个是基础特权

      // 如果有每日任务次数特权，编号递增
      if (this.currentGradeInfo.number !== undefined) {
        number++;
      }

      // 如果有抽奖次数特权，编号递增
      if (this.currentGradeInfo.daily_turntable_times !== undefined) {
        number++;
      }

      return number;
    },
  },
};
</script>
<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Source+Han+Sans+SC:wght@300;400;500;700;900&display=swap");

/* 移除默认的页面顶部padding */
.PageBox.no-padding {
  padding-top: 0 !important;
  padding-bottom: 60px;
}
.vip-page {
  background: #ffffff;
  font-family: "Source Han Sans SC", "PingFang SC", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  margin: 0;
  padding-bottom: 20px;
}

/* VIP卡片容器 */
.vip-cards-container {
  padding: 80px 24px 0;
  position: relative;
  overflow: hidden;
  min-height: 260px;
  margin-top: 0;
}

/* 轮播箭头样式 */
.swipe-arrow {
  position: absolute;
  top: 70%;
  transform: translateY(-50%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.swipe-arrow-left {
  left: 5px;
}

.swipe-arrow-right {
  right: 5px;
}

.vip-cards-container::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80%;
  background: linear-gradient(180deg, #f42937 0%, rgba(244, 41, 55, 0) 100%);
}

.vip-swipe {
  width: 350px;
  height: 185px;
  border-radius: 18px;
  overflow: hidden;
  margin: 0 auto;
}
.vip-card {
  width: 350px;
  height: 185px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 2;
  margin-bottom: 12px;
}

.vip-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

/* VIP卡片背景基础样式 */
.card-background {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* VIP等级对应的背景图片 */
.vip-level-1 {
  background: url('~@/static/images/vip1.png') no-repeat center center;
  background-size: cover;
}

.vip-level-2 {
  background: url('~@/static/images/vip2.png') no-repeat center center;
  background-size: cover;
}

.vip-level-3 {
  background: url('~@/static/images/vip3.png') no-repeat center center;
  background-size: cover;
}

.vip-level-4 {
  background: url('~@/static/images/vip4.png') no-repeat center center;
  background-size: cover;
}

.vip-level-5 {
  background: url('~@/static/images/vip5.png') no-repeat center center;
  background-size: cover;
}

.vip-level-6 {
  background: url('~@/static/images/vip6.png') no-repeat center center;
  background-size: cover;
}

.vip-level-7 {
  background: url('~@/static/images/vip7.png') no-repeat center center;
  background-size: cover;
}

.vip-level-8 {
  background: url('~@/static/images/vip8.png') no-repeat center center;
  background-size: cover;
}

.vip-level-9 {
  background: url('~@/static/images/vip9.png') no-repeat center center;
  background-size: cover;
}

/* 卡片内容 */
.card-content {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.agent-title {
  font-size: 24px;
  font-weight: bold;
  color: #000;
  line-height: 1;
  text-align: center;
}

/* VIP到期时间样式 - 卡片中间位置 */
.vip-expire-center {
  position: absolute;
  top: 71%;
  left: 6%;
  font-size: 14px;
  color: #8b6914;
  text-align: center;
  font-weight: 600;
}

/* VIP已过期状态样式 */
.vip-expired {
  color: #ff4757 !important;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



/* 卡片底部 */
.card-footer {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}



.price-tag {
  background: linear-gradient(135deg, #602f1d 0%, #915032 100%);
  color: #fff9f3;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  text-align: center;
}

/* 续费按钮 */
.renew-button {
  position: absolute;
  bottom: 15px;
  left: 20px;
  background: #a9a9a9;
  color: white;
  padding: 6px 16px;
  border-radius: 15px;
  font-size: 12px;
  z-index: 2;
}

/* 独家优势按钮 */
.exclusive-button-container {
  padding: 20px 24px;
  background: #ffffff;
}

.exclusive-button {
  width: 100%;
  background: #ff0f23 !important;
  color: white;
  border: none !important;
  border-radius: 8px !important;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.exclusive-button:hover {
  background: #d60d1f !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.exclusive-button:active {
  background: #c60d1f !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transform: translateY(0);
}

/* 禁用状态的按钮样式 - 适度的视觉反馈 */
.exclusive-button.button-disabled,
.exclusive-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.exclusive-button.button-disabled:hover,
.exclusive-button:disabled:hover {
  background: #ff0f23 !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  transform: none !important;
}

.exclusive-button.button-disabled:active,
.exclusive-button:disabled:active {
  background: #ff0f23 !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  transform: none !important;
}



/* 主要内容区域 */
.main-content {
  padding: 0 24px 0;
  background: #ffffff;
}

/* 优势列表 */
.benefits-container {
  background: white;
  padding: 20px;
  margin: 0;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f5f5f5;
  margin-bottom: 12px;
}

.benefits-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  border-color: #ff0f23;
}

.benefits-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
  font-family: "Source Han Sans SC", sans-serif;
}

.benefit-item {
  display: flex;
  margin-bottom: 24px;
  align-items: flex-start;
}

.benefit-item:last-child {
  margin-bottom: 0;
}

.benefit-number {
  font-size: 16px;
  font-weight: 600;
  color: #ff0f23;
  margin-right: 12px;
  min-width: 20px;
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: "Source Han Sans SC", sans-serif;
  display: flex;
  align-items: center;
}

.benefit-icon {
  font-size: 18px;
  margin-right: 8px;
  display: inline-block;
}

.benefit-description {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  font-family: "PingFang SC", sans-serif;
  word-wrap: break-word; /* 支持长单词换行 */
  word-break: break-word; /* 在单词边界换行，更自然 */
  white-space: normal; /* 正常换行模式 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
}

/* 新的特权内容样式 */
.privilege-content {
  padding: 16px 0;
  font-family: "PingFang SC", sans-serif;
  color: #000; /* 默认文字颜色为黑色 */
  word-wrap: break-word; /* 支持长单词换行 */
  word-break: break-word; /* 在单词边界换行，更自然 */
  white-space: normal; /* 正常换行模式 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  line-height: 1.6; /* 增加行高提升可读性 */
  text-wrap-mode: wrap; /* 强制文本换行 */
}

.privilege-content p {
  margin: 0 0 12px 0;
  line-height: 1.6;
  color: #000; /* 段落文字为黑色 */
  word-wrap: break-word; /* 支持长单词换行 */
  word-break: break-word; /* 在单词边界换行，更自然 */
  white-space: normal; /* 正常换行模式 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
}

.privilege-content strong {
  font-weight: 600;
  color: #000; /* 加粗文字为黑色 */
}

.privilege-content ul, .privilege-content ol {
  margin: 12px 0;
  padding-left: 20px;
}

.privilege-content li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #000; /* 列表项文字为黑色 */
  word-wrap: break-word; /* 支持长单词换行 */
  word-break: break-word; /* 在单词边界换行，更自然 */
  white-space: normal; /* 正常换行模式 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
}

/* 确保所有子元素都继承黑色和自动换行 */
.privilege-content * {
  color: #000 !important;
  word-wrap: break-word !important; /* 支持长单词换行 */
  word-break: break-word !important; /* 在单词边界换行，更自然 */
  white-space: normal !important; /* 正常换行模式，强制覆盖nowrap */
  overflow-wrap: break-word !important; /* 现代浏览器的换行属性 */
  text-wrap-mode: wrap !important; /* 强制文本换行 */
}

/* 默认特权内容样式 */
.default-privilege .benefit-item {
  display: flex;
  margin-bottom: 24px;
  align-items: flex-start;
}

.default-privilege .benefit-item:last-child {
  margin-bottom: 0;
}

.default-privilege .benefit-number {
  font-size: 16px;
  font-weight: 600;
  color: #ff0f23;
  margin-right: 12px;
  min-width: 20px;
}

.default-privilege .benefit-content {
  flex: 1;
}

.default-privilege .benefit-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
  font-family: "Source Han Sans SC", sans-serif;
}

.default-privilege .benefit-description {
  font-size: 13px;
  color: #000;
  line-height: 1.6;
  font-family: "PingFang SC", sans-serif;
  word-wrap: break-word; /* 支持长单词换行 */
  word-break: break-word; /* 在单词边界换行，更自然 */
  white-space: normal; /* 正常换行模式 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .vip-cards-container {
    padding: 20px 20px 0;
  }

  .main-content {
    padding: 0 20px 0;
  }

  .exclusive-button-container {
    padding: 20px 0;
  }

  .benefits-container {
    margin: 0;
    padding: 20px;
  }
}
</style>
